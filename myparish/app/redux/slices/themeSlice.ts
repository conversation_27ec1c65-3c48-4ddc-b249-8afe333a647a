import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

type ThemeColors = {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  card: string;
  border: string;
  success: string;
  warning: string;
  error: string;
};

const defaultColors: ThemeColors = {
  primary: '#3b82f6',    // blue-500
  secondary: '#8b5cf6',  // violet-500
  accent: '#ec4899',     // pink-500
  background: '#ffffff', // white
  foreground: '#111827', // gray-900
  card: '#f9fafb',       // gray-50
  border: '#e5e7eb',     // gray-200
  success: '#10b981',    // emerald-500
  warning: '#f59e0b',    // amber-500
  error: '#ef4444',      // red-500
};

const darkModeColors: ThemeColors = {
  primary: '#2563eb',    // blue-600
  secondary: '#7c3aed',  // violet-600
  accent: '#db2777',     // pink-600
  background: '#f3f4f6', // gray-100
  foreground: '#1f2937', // gray-800
  card: '#ffffff',       // white
  border: '#d1d5db',     // gray-300
  success: '#059669',    // emerald-600
  warning: '#d97706',    // amber-600
  error: '#dc2626',      // red-600
};

interface ThemeState {
  colors: ThemeColors;
  isDarkMode: boolean;
}

const initialState: ThemeState = {
  colors: defaultColors,
  isDarkMode: false,
};

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    updateColor: (state, action: PayloadAction<{ key: keyof ThemeColors; value: string }>) => {
      const { key, value } = action.payload;
      state.colors[key] = value;
    },
    resetColors: (state) => {
      state.colors = state.isDarkMode ? darkModeColors : defaultColors;
    },
    toggleDarkMode: (state) => {
      state.isDarkMode = !state.isDarkMode;
      state.colors = state.isDarkMode ? darkModeColors : defaultColors;
    },
  },
});

export const { updateColor, resetColors, toggleDarkMode } = themeSlice.actions;

export const selectTheme = (state: RootState & { theme: ThemeState }) => state.theme;

export default themeSlice.reducer;