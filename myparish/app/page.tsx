'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useAppSelector } from './redux/hooks';

export default function Home() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(true);
  const { isAuthenticated } = useAppSelector(state => state.auth);
  
  useEffect(() => {
    // Use a flag to prevent multiple redirects
    if (!isRedirecting) return;
    
    // Use Redux auth state instead of cookies
    if (isAuthenticated) {
      setIsRedirecting(false);
      router.push('/admin');
    } else {
      setIsRedirecting(false);
      router.push('/login');
    }
  }, [router, isAuthenticated, isRedirecting]);
  
  // Show loading state while redirecting
  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <Loader2 className="h-12 w-12 animate-spin text-blue-600 mb-4" />
      <p className="text-lg">Redirecting to appropriate page...</p>
    </div>
  );
}
