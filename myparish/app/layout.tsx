import './globals.css';
import { Inter } from 'next/font/google';
import ReduxProvider from './redux/ReduxProvider';
import { ThemeProvider } from './context/ThemeContext';
import AuthInitializer from './components/AuthInitializer';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'MyParish - Church Management System',
  description: 'A comprehensive church management system',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ReduxProvider>
          <ThemeProvider>
            <AuthInitializer />
            {children}
          </ThemeProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
