'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { eventsApi } from '@/lib/api';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface Event {
  _id: string;
  title: string;
  startDate: string;
  endDate: string;
  location: string;
  organizer: string;
  description?: string;
}

export default function ViewEventPage() {
  const { id } = useParams(); // Get the event ID from the URL
  const router = useRouter();

  const [event, setEvent] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setIsLoading(true);
        if (!id) {
          throw new Error('Event ID is undefined');
        }
        const data = await eventsApi.getById(id.toString()); // Fetch event by ID
        setEvent(data);
      } catch (error) {
        console.error('Failed to fetch event:', error);
        toast.error('Failed to load event details.');
        router.push('/admin/events'); // Redirect to events list if fetching fails
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvent();
  }, [id, router]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 size={32} className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!event) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p className="text-gray-500">Event not found.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Event Details</h1>
        <Link
          href="/admin/events"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Back to Events
        </Link>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md max-w-3xl mx-auto">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">{event.title}</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <p className="text-gray-600 font-medium">Start Date</p>
            <p className="text-gray-800">{new Date(event.startDate).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-gray-600 font-medium">End Date</p>
            <p className="text-gray-800">{new Date(event.endDate).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-gray-600 font-medium">Location</p>
            <p className="text-gray-800">{event.location}</p>
          </div>
          <div>
            <p className="text-gray-600 font-medium">Organizer</p>
            <p className="text-gray-800">{event.organizer}</p>
          </div>
        </div>

        {event.description && (
          <div className="mb-6">
            <p className="text-gray-600 font-medium">Description</p>
            <p className="text-gray-800">{event.description}</p>
          </div>
        )}

        <div className="flex justify-end">
          <Link
            href={`/admin/events/edit/${event._id}`}
            className="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600"
          >
            Edit Event
          </Link>
        </div>
      </div>
    </div>
  );
}