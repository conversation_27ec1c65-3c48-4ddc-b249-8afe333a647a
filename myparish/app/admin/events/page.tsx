'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { eventsApi } from '@/lib/api';
import { Edit, Eye, Trash2, Search, Plus } from 'lucide-react';
import Swal from 'sweetalert2';
import { ToastContainer, toast } from 'react-toastify';
import 'sweetalert2/dist/sweetalert2.min.css';
import 'react-toastify/dist/ReactToastify.css';

interface Event {
  _id: string;
  title: string;
  startDate: string;
  endDate: string;
  location: string;
  organizer: string;
}

export default function EventsPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setIsLoading(true);
        const data = await eventsApi.getAll();
        setEvents(data);
      } catch (error) {
        console.error('Failed to fetch events:', error);
        toast.error('Failed to load events.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'You won’t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    });

    if (result.isConfirmed) {
      try {
        await eventsApi.delete(id);
        setEvents(events.filter((event) => event._id !== id));
        toast.success('Event deleted successfully.');
      } catch (error) {
        console.error('Failed to delete event:', error);
        toast.error('Failed to delete event.');
      }
    }
  };

  const filteredEvents = events.filter((event) =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Manage Events</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-700">Events Registry</h2>
            <p className="text-gray-500">Manage all parish events</p>
          </div>
          <Link
            href="/admin/events/add"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="mr-2" size={18} />
            Add New Event
          </Link>
        </div>

        <div className="relative mb-6">
          <input
            type="text"
            placeholder="Search events..."
            className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute top-2.5 right-3 text-gray-400" size={18} />
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin text-blue-500">Loading...</div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border rounded-lg">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Title</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Start Date</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">End Date</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Location</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Organizer</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredEvents.length === 0 ? (
                  <tr>
                    <td className="py-4 px-4 text-center text-gray-500" colSpan={6}>
                      No events found.
                    </td>
                  </tr>
                ) : (
                  filteredEvents.map((event) => (
                    <tr key={event._id} className="border-t hover:bg-gray-50 transition">
                      <td className="py-3 px-4">{event.title}</td>
                      <td className="py-3 px-4">{new Date(event.startDate).toLocaleDateString()}</td>
                      <td className="py-3 px-4">{new Date(event.endDate).toLocaleDateString()}</td>
                      <td className="py-3 px-4">{event.location}</td>
                      <td className="py-3 px-4">{event.organizer}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Link href={`/admin/events/${event._id}`} className="text-blue-600 hover:text-blue-800">
                            <Eye size={18} />
                          </Link>
                          <Link href={`/admin/events/edit/${event._id}`} className="text-yellow-600 hover:text-yellow-800">
                            <Edit size={18} />
                          </Link>
                          <button
                            className="text-red-600 hover:text-red-800"
                            onClick={() => handleDelete(event._id)}
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <ToastContainer />
    </div>
  );
}
