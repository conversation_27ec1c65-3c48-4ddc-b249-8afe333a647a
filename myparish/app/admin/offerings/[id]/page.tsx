'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { offeringsApi } from '@/lib/api';
import { Loader2, Printer, ArrowLeft, Edit } from 'lucide-react';
import { useReactToPrint } from 'react-to-print';

interface Offering {
  _id: string;
  date: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  type: string;
  amount: number;
  purpose: string;
  notes?: string;
}

export default function ViewOfferingPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [offering, setOffering] = useState<Offering | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [generateBill, setGenerateBill] = useState(false);
  const receiptRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchOffering = async () => {
      try {
        setIsLoading(true);
        const data = await offeringsApi.getById(params.id);
        setOffering(data);
      } catch (err) {
        console.error('Failed to fetch offering:', err);
        setError('Failed to load offering details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOffering();
  }, [params.id]);

  const handlePrint = useReactToPrint({
    contentRef: receiptRef,
    documentTitle: `Offering_Receipt_${offering?._id || 'view'}`,
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const toggleBillGeneration = () => {
    setGenerateBill(!generateBill);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading offering details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <button
          onClick={() => router.back()}
          className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>
      </div>
    );
  }

  if (!offering) {
    return (
      <div className="p-4">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          Offering not found
        </div>
        <button
          onClick={() => router.push('/admin/offerings')}
          className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offerings
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Offering Details</h1>
        <div className="flex gap-4">
          <button
            onClick={toggleBillGeneration}
            className="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300"
          >
            View as {generateBill ? "Receipt" : "Bill"}
          </button>
          <button
            onClick={handlePrint}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print {generateBill ? "Bill" : "Receipt"}
          </button>
          <Link
            href={`/admin/offerings/edit/${offering._id}`}
            className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 inline-flex items-center"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Offering
          </Link>
          <Link
            href="/admin/offerings"
            className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 inline-flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Offerings
          </Link>
        </div>
      </div>
      
      {/* Receipt/Bill Template */}
      <div ref={receiptRef} className="bg-white p-8 rounded-lg shadow-md max-w-2xl mx-auto">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold">My Parish</h2>
          <p className="text-gray-600">
            {generateBill ? "Official Bill" : "Official Offering Receipt"}
          </p>
        </div>
        
        <div className="mb-6">
          <h3 className="font-semibold mb-2">Family Information</h3>
          <div className="bg-gray-50 p-4 rounded">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-600">Family ID</p>
                <p className="font-medium">{offering.familyId.slNo}</p>
              </div>
              <div>
                <p className="text-gray-600">Family Name</p>
                <p className="font-medium">{offering.familyId.headName}</p>
              </div>
              <div>
                <p className="text-gray-600">Receipt ID</p>
                <p className="font-medium">{offering._id}</p>
              </div>
              <div>
                <p className="text-gray-600">Date</p>
                <p className="font-medium">{formatDate(offering.date)}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <h3 className="font-semibold mb-2">{generateBill ? "Bill Details" : "Offering Details"}</h3>
          <div className="bg-gray-50 p-4 rounded">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-600">Type</p>
                <p className="font-medium">{offering.type}</p>
              </div>
              <div>
                <p className="text-gray-600">Amount</p>
                <p className="font-medium">${Number(offering.amount).toFixed(2)}</p>
              </div>
              {offering.purpose && (
                <div className="col-span-2">
                  <p className="text-gray-600">Purpose</p>
                  <p className="font-medium">{offering.purpose}</p>
                </div>
              )}
              {offering.notes && (
                <div className="col-span-2">
                  <p className="text-gray-600">Notes</p>
                  <p className="font-medium">{offering.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="text-center text-gray-500 text-sm mt-8">
          {generateBill ? (
            <p>Please pay the above amount at your earliest convenience.</p>
          ) : (
            <p>Thank you for your generous contribution!</p>
          )}
          <p className="mt-1">This {generateBill ? "bill" : "receipt"} was generated on {formatDate(new Date().toISOString())}</p>
        </div>
      </div>
    </div>
  );
}
