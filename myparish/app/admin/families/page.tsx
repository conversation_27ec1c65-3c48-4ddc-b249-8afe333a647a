'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { familiesApi } from '@/lib/api';
import { Edit, Eye, Trash2, Search, Plus } from 'lucide-react';
import Swal from 'sweetalert2';
import { ToastContainer, toast } from 'react-toastify';
import 'sweetalert2/dist/sweetalert2.min.css';
import 'react-toastify/dist/ReactToastify.css';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
  membersCount: number;
  address: string;
}

export default function FamiliesPage() {
  const [families, setFamilies] = useState<Family[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchFamilies = async () => {
      try {
        setIsLoading(true);
        const data = await familiesApi.getAll();
        setFamilies(data);
      } catch (error) {
        console.error('Failed to fetch families:', error);
        toast.error('Failed to load families.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFamilies();
  }, []);

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'You won’t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    });

    if (result.isConfirmed) {
      try {
        await familiesApi.delete(id);
        setFamilies(families.filter((family) => family._id !== id));
        toast.success('Family deleted successfully.');
      } catch (error) {
        console.error('Failed to delete family:', error);
        toast.error('Failed to delete family.');
      }
    }
  };

  const filteredFamilies = families.filter((family) =>
    family.headName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Manage Families</h1>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-700">Families Registry</h2>
            <p className="text-gray-500">Manage all registered families</p>
          </div>
          <Link
            href="/admin/families/add"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="mr-2" size={18} />
            Add New Family
          </Link>
        </div>

        <div className="relative mb-6">
          <input
            type="text"
            placeholder="Search families..."
            className="w-full p-2 border rounded-lg focus:ring focus:ring-blue-300"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute top-2.5 right-3 text-gray-400" size={18} />
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin text-blue-500">Loading...</div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border rounded-lg">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">ID</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Head Name</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Members</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Address</th>
                  <th className="py-3 px-4 text-left text-gray-600 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredFamilies.length === 0 ? (
                  <tr>
                    <td className="py-4 px-4 text-center text-gray-500" colSpan={5}>
                      No families found.
                    </td>
                  </tr>
                ) : (
                  filteredFamilies.map((family) => (
                    <tr key={family._id} className="border-t hover:bg-gray-50 transition">
                      <td className="py-3 px-4">{family.slNo}</td>
                      <td className="py-3 px-4">{family.headName}</td>
                      <td className="py-3 px-4">{family.membersCount}</td>
                      <td className="py-3 px-4">{family.address}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Link href={`/admin/families/${family._id}`} className="text-blue-600 hover:text-blue-800">
                            <Eye size={18} />
                          </Link>
                          <Link href={`/admin/families/edit/${family._id}`} className="text-yellow-600 hover:text-yellow-800">
                            <Edit size={18} />
                          </Link>
                          <button
                            className="text-red-600 hover:text-red-800"
                            onClick={() => handleDelete(family._id)}
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <ToastContainer />
    </div>
  );
}
