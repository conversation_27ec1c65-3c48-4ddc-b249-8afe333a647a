'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, Users, Home, Calendar, BookOpen, Briefcase, Heart, FileText, X, Plus, 
  Camera, Mail, Phone, Trash2, Save, ArrowLeft, Hash, MapPin, Building, 
  DollarSign, Home as HouseIcon, UserPlus, UserCheck
} from 'lucide-react';
import { familiesApi } from '@/lib/api';

export default function AddFamilyPage() {
  const router = useRouter();
  const initialMember = {
    name: '',
    relationship: '',
    dob: '',
    baptism: '',
    hc: '',
    uruthipoosal: '',
    marriage: '',
    education: '',
    occupation: '',
    photo: null
  };

  const [formData, setFormData] = useState({
    slNo: '',
    headName: '',
    gender: '',
    uraviam: '',
    houseNo: '',
    address: '',
    wardP: '',
    wardC: '',
    occupation: '',
    annualIncome: '',
    houseType: '',
    infrastructure: '',
    headDOB: '',
    headBaptism: '',
    headHC: '',
    headUruthipoosal: '',
    headMarriage: '',
    headEducation: '',
    spouseName: '',
    spouseDOB: '',
    spouseBaptism: '',
    spouseHC: '',
    spouseUruthipoosal: '',
    spouseOccupation: '',
    spouseEducation: '',
    members: [initialMember],
    headPhoto: null,
    spousePhoto: null,
    phone: '',
    email: ''
  });

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, key: string) => {
    setFormData({ ...formData, [key]: e.target.files ? e.target.files[0] : null });
  };

  const handleMemberChange = (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const files = e.target instanceof HTMLInputElement ? e.target.files : null;
    const updatedMembers = [...formData.members];
    updatedMembers[index] = {
      ...updatedMembers[index],
      [name]: files ? files[0] : value
    };
    setFormData({ ...formData, members: updatedMembers });
  };

  const addMember = () => {
    setFormData({ ...formData, members: [...formData.members, initialMember] });
  };

  const removeMember = (index: number) => {
    const updatedMembers = formData.members.filter((_, i) => i !== index);
    setFormData({ ...formData, members: updatedMembers });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Create FormData object for file uploads
      const formDataObj = new FormData();
      
      // Add family data as JSON string
      formDataObj.append('familyData', JSON.stringify(formData));
      
      // Add file uploads if they exist
      if (formData.headPhoto) {
        formDataObj.append('headPhoto', formData.headPhoto);
      }
      
      if (formData.spousePhoto) {
        formDataObj.append('spousePhoto', formData.spousePhoto);
      }
      
      // Add member photos if they exist
      formData.members.forEach((member, index) => {
        if (member.photo) {
          formDataObj.append('memberPhotos', member.photo);
        }
      });
      
      // Submit to API
      await familiesApi.create(formDataObj);
      
      // Redirect on success
      router.push('/admin/families');
    } catch (error) {
      console.error('Error submitting family:', error);
      // Handle error (show toast notification, etc.)
    }
  };

  const genderOptions = ['Male', 'Female'];
  const uraviamOptions = ['Option A', 'Option B', 'Option C', 'Option D'];
  const infraOptions = ['House', 'Hut', 'Apartment', 'Villa'];
  const educationOptions = ['None', 'Primary', 'Secondary', 'Higher Secondary', 'Graduate', 'Post Graduate', 'Doctorate'];
  const relationshipOptions = ['Son', 'Daughter', 'Father', 'Mother', 'Brother', 'Sister', 'Other'];

  return (
    <div className="p-4 md:p-8 max-w-7xl mx-auto bg-gray-50 min-h-screen">
      <div className="bg-white shadow-lg rounded-xl p-6 mb-8 border border-gray-100">
        <h1 className="text-2xl font-bold mb-6 text-indigo-700 flex items-center">
          <UserPlus className="mr-2" /> Family Registration Form
        </h1>
        
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between">
            {[
              { step: 1, title: "Basic Info", icon: <Home className="h-5 w-5" /> },
              { step: 2, title: "Family Details", icon: <User className="h-5 w-5" /> },
              { step: 3, title: "Members", icon: <Users className="h-5 w-5" /> }
            ].map((item) => (
              <div key={item.step} className="flex flex-col items-center">
                <div 
                  className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                    item.step === currentStep 
                      ? 'bg-indigo-600 text-white' 
                      : item.step < currentStep 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-200 text-gray-700'
                  }`}
                >
                  {item.step < currentStep ? '✓' : item.icon}
                </div>
                <span className="text-sm text-gray-600">{item.title}</span>
              </div>
            ))}
          </div>
          <div className="mt-2 h-2 bg-gray-200 rounded-full">
            <div 
              className="h-full bg-indigo-600 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
            ></div>
          </div>
        </div>
        
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <BasicInfoForm 
              key="step1"
              formData={formData}
              handleChange={handleChange}
              genderOptions={genderOptions}
              uraviamOptions={uraviamOptions}
              infraOptions={infraOptions}
              nextStep={nextStep}
              router={router}
            />
          )}
          
          {currentStep === 2 && (
            <FamilyDetailsForm 
              key="step2"
              formData={formData}
              handleChange={handleChange}
              handleFileChange={handleFileChange}
              educationOptions={educationOptions}
              nextStep={nextStep}
              prevStep={prevStep}
            />
          )}
          
          {currentStep === 3 && (
            <MembersForm 
              key="step3"
              formData={formData}
              handleMemberChange={handleMemberChange}
              addMember={addMember}
              removeMember={removeMember}
              relationshipOptions={relationshipOptions}
              educationOptions={educationOptions}
              prevStep={prevStep}
              handleSubmit={handleSubmit}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

interface BasicInfoFormProps {
  formData: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  genderOptions: string[];
  uraviamOptions: string[];
  infraOptions: string[];
  nextStep: () => void;
  router: any;
}

function BasicInfoForm({ 
  formData, 
  handleChange, 
  genderOptions, 
  uraviamOptions, 
  infraOptions, 
  nextStep, 
  router 
}: BasicInfoFormProps) {
  
  const handleNext = (e: React.FormEvent) => {
    e.preventDefault();
    nextStep();
  };
  
  return (
    <motion.form
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      onSubmit={handleNext}
    >
      <section>
        <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm">
          <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
            <Home className="mr-2 h-5 w-5" /> Basic Family Information
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="input-group">
            <label className="input-label flex items-center">
              <Hash className="h-4 w-4 mr-1 text-indigo-500" /> Serial No
            </label>
            <input 
              name="slNo" 
              value={formData.slNo} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Family serial number"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <User className="h-4 w-4 mr-1 text-indigo-500" /> Family Head Name
            </label>
            <input 
              name="headName" 
              value={formData.headName} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Enter full name"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <UserCheck className="h-4 w-4 mr-1 text-indigo-500" /> Gender
            </label>
            <select 
              name="gender" 
              value={formData.gender} 
              onChange={handleChange} 
              className="input-field"
              required
            >
              <option value="">Select Gender</option>
              {genderOptions.map(g => <option key={g} value={g}>{g}</option>)}
            </select>
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Users className="h-4 w-4 mr-1 text-indigo-500" /> Uraviam
            </label>
            <select 
              name="uraviam" 
              value={formData.uraviam} 
              onChange={handleChange} 
              className="input-field"
              required
            >
              <option value="">Select Uraviam</option>
              {uraviamOptions.map(u => <option key={u} value={u}>{u}</option>)}
            </select>
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Building className="h-4 w-4 mr-1 text-indigo-500" /> House No
            </label>
            <input 
              name="houseNo" 
              value={formData.houseNo} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="House number"
              required
            />
          </div>
          
          <div className="input-group col-span-1 md:col-span-3">
            <label className="input-label flex items-center">
              <MapPin className="h-4 w-4 mr-1 text-indigo-500" /> Address
            </label>
            <input 
              name="address" 
              value={formData.address} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Full residential address"
              required
            />
          </div>
        
          <div className="input-group">
            <label className="input-label flex items-center">
              <MapPin className="h-4 w-4 mr-1 text-indigo-500" /> Permanent Ward No
            </label>
            <input 
              name="wardP" 
              value={formData.wardP} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Permanent ward"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <MapPin className="h-4 w-4 mr-1 text-indigo-500" /> Current Ward No
            </label>
            <input 
              name="wardC" 
              value={formData.wardC} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Current ward"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Phone className="h-4 w-4 mr-1 text-indigo-500" /> Phone Number
            </label>
            <input 
              name="phone" 
              value={formData.phone} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Contact number"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Mail className="h-4 w-4 mr-1 text-indigo-500" /> Email
            </label>
            <input 
              name="email" 
              type="email"
              value={formData.email} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Email address"
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Briefcase className="h-4 w-4 mr-1 text-indigo-500" /> Occupation
            </label>
            <input 
              name="occupation" 
              value={formData.occupation} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Head's occupation"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <DollarSign className="h-4 w-4 mr-1 text-indigo-500" /> Annual Income
            </label>
            <input 
              name="annualIncome" 
              value={formData.annualIncome} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Yearly income"
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <HouseIcon className="h-4 w-4 mr-1 text-indigo-500" /> House Type
            </label>
            <select 
              name="houseType" 
              value={formData.houseType} 
              onChange={handleChange} 
              className="input-field"
              required
            >
              <option value="">Select House Type</option>
              <option value="Own">Own</option>
              <option value="Rental">Rental</option>
            </select>
          </div>
          
          <div className="input-group">
            <label className="input-label flex items-center">
              <Building className="h-4 w-4 mr-1 text-indigo-500" /> Infrastructure
            </label>
            <select 
              name="infrastructure" 
              value={formData.infrastructure} 
              onChange={handleChange} 
              className="input-field"
              required
            >
              <option value="">Select Infrastructure</option>
              {infraOptions.map(i => <option key={i} value={i}>{i}</option>)}
            </select>
          </div>
        </div>
      </section>

      <div className="flex justify-end mt-8">
        <button 
          type="button" 
          onClick={() => router.push('/admin/families')} 
          className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md mr-4 transition-colors duration-200 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Cancel
        </button>
        <button 
          type="submit" 
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-2 rounded-md shadow-lg transition-colors duration-200 flex items-center"
        >
          Next <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
        </button>
      </div>
    </motion.form>
  );
}

interface FamilyDetailsFormProps {
  formData: any;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>, key: string) => void;
  educationOptions: string[];
  nextStep: () => void;
  prevStep: () => void;
}

function FamilyDetailsForm({ 
  formData, 
  handleChange, 
  handleFileChange, 
  educationOptions, 
  nextStep, 
  prevStep 
}: FamilyDetailsFormProps) {
  
  const handleNext = (e: React.FormEvent) => {
    e.preventDefault();
    nextStep();
  };
  
  return (
    <motion.form
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      onSubmit={handleNext}
    >
      {/* Head of Family Section */}
      <section className="mb-8">
        <div className="bg-indigo-50 p-3 rounded-md mb-4">
          <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
            <User className="mr-2 h-5 w-5" /> Head of Family Details
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="input-group">
            <label className="input-label">Date of Birth</label>
            <input 
              type="date" 
              name="headDOB" 
              value={formData.headDOB} 
              onChange={handleChange} 
              className="input-field" 
              required
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Baptism Date</label>
            <input 
              type="date" 
              name="headBaptism" 
              value={formData.headBaptism} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Holy Communion Date</label>
            <input 
              type="date" 
              name="headHC" 
              value={formData.headHC} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Uruthipoosal Date</label>
            <input 
              type="date" 
              name="headUruthipoosal" 
              value={formData.headUruthipoosal} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Marriage Date</label>
            <input 
              type="date" 
              name="headMarriage" 
              value={formData.headMarriage} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Education</label>
            <select 
              name="headEducation" 
              value={formData.headEducation} 
              onChange={handleChange} 
              className="input-field"
            >
              <option value="">Select Education</option>
              {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
            </select>
          </div>
          
          <div className="input-group md:col-span-3">
            <label className="input-label">Upload Head Photo</label>
            <div className="flex items-center space-x-2">
              <div className="relative w-full">
                <input 
                  type="file" 
                  onChange={(e) => handleFileChange(e, 'headPhoto')} 
                  className="file-input" 
                  accept="image/*"
                />
              </div>
              {formData.headPhoto && (
                <div className="text-sm text-green-600">Photo selected</div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Spouse Section */}
      <section className="mb-8">
        <div className="bg-indigo-50 p-3 rounded-md mb-4">
          <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
            <User className="mr-2 h-5 w-5" /> Spouse Details
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="input-group">
            <label className="input-label">Spouse Name</label>
            <input 
              name="spouseName" 
              value={formData.spouseName} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Spouse's full name"
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Date of Birth</label>
            <input 
              type="date" 
              name="spouseDOB" 
              value={formData.spouseDOB} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Baptism Date</label>
            <input 
              type="date" 
              name="spouseBaptism" 
              value={formData.spouseBaptism} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Holy Communion Date</label>
            <input 
              type="date" 
              name="spouseHC" 
              value={formData.spouseHC} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Uruthipoosal Date</label>
            <input 
              type="date" 
              name="spouseUruthipoosal" 
              value={formData.spouseUruthipoosal} 
              onChange={handleChange} 
              className="input-field" 
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Occupation</label>
            <input 
              name="spouseOccupation" 
              value={formData.spouseOccupation} 
              onChange={handleChange} 
              className="input-field" 
              placeholder="Spouse's occupation"
            />
          </div>
          
          <div className="input-group">
            <label className="input-label">Education</label>
            <select 
              name="spouseEducation" 
              value={formData.spouseEducation} 
              onChange={handleChange} 
              className="input-field"
            >
              <option value="">Select Education</option>
              {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
            </select>
          </div>
          
          <div className="input-group md:col-span-3">
            <label className="input-label">Upload Spouse Photo</label>
            <div className="flex items-center space-x-2">
              <div className="relative w-full">
                <input 
                  type="file" 
                  onChange={(e) => handleFileChange(e, 'spousePhoto')} 
                  className="file-input" 
                  accept="image/*"
                />
              </div>
              {formData.spousePhoto && (
                <div className="text-sm text-green-600">Photo selected</div>
              )}
            </div>
          </div>
        </div>
      </section>

      <div className="flex justify-between mt-8">
        <button 
          type="button" 
          onClick={prevStep} 
          className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md mr-4 transition-colors duration-200 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Back
        </button>
        <button 
          type="submit" 
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-2 rounded-md shadow-lg transition-colors duration-200 flex items-center"
        >
          Next <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
        </button>
      </div>
    </motion.form>
  );
}

interface MembersFormProps {
  formData: any;
  handleMemberChange: (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  addMember: () => void;
  removeMember: (index: number) => void;
  relationshipOptions: string[];
  educationOptions: string[];
  prevStep: () => void;
  handleSubmit: (e: React.FormEvent) => void;
}

function MembersForm({ 
  formData, 
  handleMemberChange, 
  addMember, 
  removeMember, 
  relationshipOptions, 
  educationOptions, 
  prevStep, 
  handleSubmit 
}: MembersFormProps) {
  
  const handleNext = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(e);
  };
  
  return (
    <motion.form
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      onSubmit={handleNext}
    >
      {/* Family Members Section */}
      <section className="mb-8">
        <div className="bg-indigo-50 p-3 rounded-md mb-4 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
            <Users className="mr-2 h-5 w-5" /> Family Members
          </h2>
          <button 
            type="button" 
            onClick={addMember} 
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center transition-colors duration-200"
          >
            <Plus className="mr-1 h-4 w-4" /> Add Member
          </button>
        </div>
        
        {formData.members.map((member:any, index: number) => (
          <div key={index} className="mb-6 p-4 border border-gray-200 rounded-lg shadow-sm bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium text-indigo-600">Member #{index + 1}</h3>
              {index > 0 && (
                <button 
                  type="button" 
                  onClick={() => removeMember(index)} 
                  className="text-red-600 hover:text-red-800 flex items-center"
                >
                  <Trash2 className="h-4 w-4 mr-1" /> Remove
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="input-group">
                <label className="input-label">Name</label>
                <input 
                  name="name" 
                  value={member.name} 
                  onChange={(e) => handleMemberChange(index, e)} 
                  className="input-field" 
                  placeholder="Member's full name"
                />
              </div>
              
              <div className="input-group">
                <label className="input-label">Relationship</label>
                <select
                  name="relationship" 
                  value={member.relationship} 
                  onChange={(e) => handleMemberChange(index, e)} 
                  className="input-field"
                >
                  <option value="">Select Relationship</option>
                  {relationshipOptions.map(r => <option key={r} value={r}>{r}</option>)}
                </select>
              </div>
              
              <div className="input-group">
                <label className="input-label">Date of Birth</label>
                <div className="relative">
                  <input 
                    type="date" 
                    name="dob" 
                    value={member.dob} 
                    onChange={(e) => handleMemberChange(index, e)} 
                    className="input-field pl-10" 
                  />
                </div>
              </div>
              
              <div className="input-group">
                <label className="input-label">Baptism Date</label>
                <input 
                  type="date" 
                  name="baptism" 
                  value={member.baptism} 
                  onChange={(e) => handleMemberChange(index, e)} 
                  className="input-field" 
                />
              </div>
              
              <div className="input-group">
                <label className="input-label">Holy Communion Date</label>
                <input 
                  type="date" 
                  name="hc" 
                  value={member.hc} 
                  onChange={(e) => handleMemberChange(index, e)} 
                  className="input-field" 
                />
              </div>
              
              <div className="input-group">
                <label className="input-label">Uruthipoosal Date</label>
                <input 
                  type="date" 
                  name="uruthipoosal" 
                  value={member.uruthipoosal} 
                  onChange={(e) => handleMemberChange(index, e)} 
                  className="input-field" 
                />
              </div>
              
              <div className="input-group">
                <label className="input-label">Marriage Date</label>
                <div className="relative">
                  <input 
                    type="date" 
                    name="marriage" 
                    value={member.marriage} 
                    onChange={(e) => handleMemberChange(index, e)} 
                    className="input-field pl-10" 
                  />
                </div>
              </div>
              
              <div className="input-group">
                <label className="input-label">Education</label>
                <div className="relative">
                  <select 
                    name="education" 
                    value={member.education} 
                    onChange={(e) => handleMemberChange(index, e)} 
                    className="input-field pl-10"
                  >
                    <option value="">Select Education</option>
                    {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
                  </select>
                </div>
              </div>
              
              <div className="input-group">
                <label className="input-label">Occupation</label>
                <div className="relative">
                  <input 
                    name="occupation" 
                    value={member.occupation} 
                    onChange={(e) => handleMemberChange(index, e)} 
                    className="input-field pl-10" 
                    placeholder="Member's occupation"
                  />
                </div>
              </div>
              
              <div className="input-group md:col-span-3">
                <label className="input-label">Upload Photo</label>
                <div className="flex items-center space-x-2">
                  <div className="relative w-full">
                    <input 
                      type="file" 
                      name="photo" 
                      onChange={(e) => handleMemberChange(index, e)} 
                      className="file-input pl-10" 
                      accept="image/*"
                    />
                  </div>
                  {member.photo && (
                    <div className="text-sm text-green-600">Photo selected</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </section>

      <div className="flex justify-between mt-8">
        <button 
          type="button" 
          onClick={prevStep} 
          className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md mr-4 transition-colors duration-200 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Back
        </button>
        <button 
          type="submit" 
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-2 rounded-md shadow-lg transition-colors duration-200 flex items-center"
        >
          Save Family
        </button>
      </div>
    </motion.form>
  );
}
