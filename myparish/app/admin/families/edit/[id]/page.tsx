'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  User, Users, Home, Calendar, BookOpen, Briefcase, Heart, 
  ArrowLeft, Hash, MapPin, Building, DollarSign, 
  Home as HouseIcon, UserPlus, UserCheck, Mail, Phone, Trash2, Save, Plus, X, Camera
} from 'lucide-react';
import { familiesApi } from '@/lib/api';

// Define interfaces for our data types
interface Member {
  name: string;
  relationship: string;
  dob: string | Date;
  baptism?: string | Date;
  hc?: string | Date;
  uruthipoosal?: string | Date;
  marriage?: string | Date;
  education?: string;
  occupation?: string;
  photo?: string | File | null;
}

interface FamilyFormData {
  _id: string;
  slNo: string;
  headName: string;
  gender: string;
  uraviam: string;
  houseNo: string;
  address: string;
  wardP: string;
  wardC: string;
  occupation: string;
  annualIncome: string;
  houseType: string;
  infrastructure: string;
  headDOB: string;
  headBaptism: string;
  headHC: string;
  headUruthipoosal: string;
  headMarriage: string;
  headEducation: string;
  spouseName: string;
  spouseDOB: string;
  spouseBaptism: string;
  spouseHC: string;
  spouseUruthipoosal: string;
  spouseOccupation: string;
  spouseEducation: string;
  members: Member[];
  headPhoto: string | File | null;
  spousePhoto: string | File | null;
  phone: string;
  email: string;
  notes: string;
  specialNeeds: string;
}

export default function EditFamilyPage() {
  const params = useParams();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const initialMember: Member = {
    name: '',
    relationship: '',
    dob: '',
    baptism: '',
    hc: '',
    uruthipoosal: '',
    marriage: '',
    education: '',
    occupation: '',
    photo: null
  };

  const [formData, setFormData] = useState<FamilyFormData>({
    _id: '',
    slNo: '',
    headName: '',
    gender: '',
    uraviam: '',
    houseNo: '',
    address: '',
    wardP: '',
    wardC: '',
    occupation: '',
    annualIncome: '',
    houseType: '',
    infrastructure: '',
    headDOB: '',
    headBaptism: '',
    headHC: '',
    headUruthipoosal: '',
    headMarriage: '',
    headEducation: '',
    spouseName: '',
    spouseDOB: '',
    spouseBaptism: '',
    spouseHC: '',
    spouseUruthipoosal: '',
    spouseOccupation: '',
    spouseEducation: '',
    members: [initialMember],
    headPhoto: null,
    spousePhoto: null,
    phone: '',
    email: '',
    notes: '',
    specialNeeds: ''
  });

  // Options for select inputs
  const genderOptions = ['Male', 'Female'];
  const uraviamOptions = ['Option A', 'Option B', 'Option C', 'Option D'];
  const infraOptions = ['House', 'Hut', 'Apartment', 'Villa'];
  const educationOptions = ['None', 'Primary', 'Secondary', 'Higher Secondary', 'Graduate', 'Post Graduate', 'Doctorate'];
  const relationshipOptions = ['Son', 'Daughter', 'Father', 'Mother', 'Brother', 'Sister', 'Other'];

  // Fetch family data
  useEffect(() => {
    const fetchFamilyDetails = async () => {
      try {
        setIsLoading(true);
        const data = await familiesApi.getById(params.id as string);
        
        // Format dates for form inputs
        const formatDateForInput = (dateString: string | Date | undefined) => {
          if (!dateString) return '';
          const date = new Date(dateString);
          return date.toISOString().split('T')[0];
        };
        
        // Prepare data for form
        setFormData({
          _id: data._id,
          slNo: data.slNo || '',
          headName: data.headName || '',
          gender: data.gender || '',
          uraviam: data.uraviam || '',
          houseNo: data.houseNo || '',
          address: data.address || '',
          wardP: data.wardP || '',
          wardC: data.wardC || '',
          occupation: data.occupation || '',
          annualIncome: data.annualIncome || '',
          houseType: data.houseType || '',
          infrastructure: data.infrastructure || '',
          headDOB: formatDateForInput(data.headDOB),
          headBaptism: formatDateForInput(data.headBaptism),
          headHC: formatDateForInput(data.headHC),
          headUruthipoosal: formatDateForInput(data.headUruthipoosal),
          headMarriage: formatDateForInput(data.headMarriage),
          headEducation: data.headEducation || '',
          spouseName: data.spouseName || '',
          spouseDOB: formatDateForInput(data.spouseDOB),
          spouseBaptism: formatDateForInput(data.spouseBaptism),
          spouseHC: formatDateForInput(data.spouseHC),
          spouseUruthipoosal: formatDateForInput(data.spouseUruthipoosal),
          spouseOccupation: data.spouseOccupation || '',
          spouseEducation: data.spouseEducation || '',
          members: data.members && data.members.length > 0 ? data.members.map((member: Member) => ({
            ...member,
            dob: formatDateForInput(member.dob),
            baptism: formatDateForInput(member.baptism),
            hc: formatDateForInput(member.hc),
            uruthipoosal: formatDateForInput(member.uruthipoosal),
            marriage: formatDateForInput(member.marriage)
          })) : [initialMember],
          headPhoto: data.headPhoto || null,
          spousePhoto: data.spousePhoto || null,
          phone: data.phone || '',
          email: data.email || '',
          notes: data.notes || '',
          specialNeeds: data.specialNeeds || ''
        });
      } catch (err) {
        console.error('Failed to fetch family details:', err);
        setError('Failed to load family details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchFamilyDetails();
    }
  }, [params.id]);

  // Navigation functions
  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  // Form change handlers
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, key: string) => {
    setFormData({ ...formData, [key]: e.target.files ? e.target.files[0] : null });
  };

  const handleMemberChange = (index: number, e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const files = e.target instanceof HTMLInputElement ? e.target.files : null;
    const updatedMembers = [...formData.members];
    updatedMembers[index] = {
      ...updatedMembers[index],
      [name]: files ? files[0] : value
    };
    setFormData({ ...formData, members: updatedMembers });
  };

  const addMember = () => {
    setFormData({ ...formData, members: [...formData.members, initialMember] });
  };

  const removeMember = (index: number) => {
    const updatedMembers = formData.members.filter((_, i) => i !== index);
    setFormData({ ...formData, members: updatedMembers });
  };

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      
      // Create FormData object for file uploads
      const formDataObj = new FormData();
      
      // Add family data as JSON string
      formDataObj.append('familyData', JSON.stringify(formData));
      
      // Add file uploads if they exist and are new files (not strings)
      if (formData.headPhoto && typeof formData.headPhoto !== 'string') {
        formDataObj.append('headPhoto', formData.headPhoto);
      }
      
      if (formData.spousePhoto && typeof formData.spousePhoto !== 'string') {
        formDataObj.append('spousePhoto', formData.spousePhoto);
      }
      
      // Add member photos if they exist and are new files
      formData.members.forEach((member, index) => {
        if (member.photo && typeof member.photo !== 'string') {
          formDataObj.append('memberPhotos', member.photo as File);
        }
      });
      
      // Submit to API
      await familiesApi.update(formData._id, formDataObj);
      
      // Redirect on success
      router.push(`/admin/families/${formData._id}`);
    } catch (error) {
      console.error('Error updating family:', error);
      setError('Failed to update family. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={() => router.push('/admin/families')}
          className="bg-indigo-600 text-white px-4 py-2 rounded-md"
        >
          Back to Families
        </button>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-8 max-w-7xl mx-auto bg-gray-50 min-h-screen">
      <div className="bg-white shadow-lg rounded-xl p-6 mb-8 border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-indigo-700 flex items-center">
            <UserPlus className="mr-2" /> Edit Family
          </h1>
          <button 
            onClick={() => router.push(`/admin/families/${formData._id}`)}
            className="flex items-center text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="mr-1" /> Back to Details
          </button>
        </div>
        
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between">
            {[
              { step: 1, title: "Basic Info", icon: <Home className="h-5 w-5" /> },
              { step: 2, title: "Family Details", icon: <User className="h-5 w-5" /> },
              { step: 3, title: "Members", icon: <Users className="h-5 w-5" /> }
            ].map((item) => (
              <div key={item.step} className="flex flex-col items-center">
                <div 
                  className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                    item.step === currentStep 
                      ? 'bg-indigo-600 text-white' 
                      : item.step < currentStep 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-200 text-gray-700'
                  }`}
                  onClick={() => setCurrentStep(item.step)}
                  style={{ cursor: 'pointer' }}
                >
                  {item.step < currentStep ? '✓' : item.icon}
                </div>
                <span className="text-sm text-gray-600">{item.title}</span>
              </div>
            ))}
          </div>
          <div className="mt-2 h-2 bg-gray-200 rounded-full">
            <div 
              className="h-full bg-indigo-600 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
            ></div>
          </div>
        </div>
        
        {/* Step 1: Basic Info */}
        {currentStep === 1 && (
          <motion.form
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            onSubmit={(e) => { e.preventDefault(); nextStep(); }}
          >
            <section>
              <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm">
                <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
                  <Home className="mr-2 h-5 w-5" /> Basic Family Information
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Hash className="h-4 w-4 mr-1 text-indigo-500" /> Serial No
                  </label>
                  <input 
                    name="slNo" 
                    value={formData.slNo} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Family serial number"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <User className="h-4 w-4 mr-1 text-indigo-500" /> Family Head Name
                  </label>
                  <input 
                    name="headName" 
                    value={formData.headName} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Enter full name"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Gender</label>
                  <select 
                    name="gender" 
                    value={formData.gender} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                    required
                  >
                    <option value="">Select Gender</option>
                    {genderOptions.map(g => <option key={g} value={g}>{g}</option>)}
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Uraviam</label>
                  <select 
                    name="uraviam" 
                    value={formData.uraviam} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select Uraviam</option>
                    {uraviamOptions.map(u => <option key={u} value={u}>{u}</option>)}
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-indigo-500" /> House No
                  </label>
                  <input 
                    name="houseNo" 
                    value={formData.houseNo} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="House number"
                    required
                  />
                </div>
                
                <div className="input-group md:col-span-2">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-indigo-500" /> Address
                  </label>
                  <input 
                    name="address" 
                    value={formData.address} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Full address"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Ward (Parish)</label>
                  <input 
                    name="wardP" 
                    value={formData.wardP} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Parish ward"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Ward (Church)</label>
                  <input 
                    name="wardC" 
                    value={formData.wardC} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Church ward"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Briefcase className="h-4 w-4 mr-1 text-indigo-500" /> Occupation
                  </label>
                  <input 
                    name="occupation" 
                    value={formData.occupation} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Head's occupation"
                    required
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <DollarSign className="h-4 w-4 mr-1 text-indigo-500" /> Annual Income
                  </label>
                  <input 
                    name="annualIncome" 
                    value={formData.annualIncome} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Annual income"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">House Type</label>
                  <select 
                    name="houseType" 
                    value={formData.houseType} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select House Type</option>
                    <option value="Own">Own</option>
                    <option value="Rent">Rent</option>
                    <option value="Lease">Lease</option>
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Infrastructure</label>
                  <select 
                    name="infrastructure" 
                    value={formData.infrastructure} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select Infrastructure</option>
                    {infraOptions.map(i => <option key={i} value={i}>{i}</option>)}
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Phone className="h-4 w-4 mr-1 text-indigo-500" /> Phone
                  </label>
                  <input 
                    name="phone" 
                    value={formData.phone} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Contact number"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Mail className="h-4 w-4 mr-1 text-indigo-500" /> Email
                  </label>
                  <input 
                    name="email" 
                    value={formData.email} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Email address"
                    type="email"
                  />
                </div>
              </div>
            </section>
            
            <div className="mt-6 flex justify-end">
              <button 
                type="submit" 
                className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700"
              >
                Next Step
              </button>
            </div>
          </motion.form>
        )}
        
        {/* Step 2: Family Head & Spouse Details */}
        {currentStep === 2 && (
          <motion.form
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            onSubmit={(e) => { e.preventDefault(); nextStep(); }}
          >
            <section className="mb-8">
              <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm">
                <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
                  <User className="mr-2 h-5 w-5" /> Family Head Details
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-indigo-500" /> Date of Birth
                  </label>
                  <input 
                    type="date" 
                    name="headDOB" 
                    value={formData.headDOB} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <BookOpen className="h-4 w-4 mr-1 text-indigo-500" /> Education
                  </label>
                  <select 
                    name="headEducation" 
                    value={formData.headEducation} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select Education Level</option>
                    {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1 flex items-center">
                    <Camera className="h-4 w-4 mr-1 text-indigo-500" /> Photo
                  </label>
                  <input 
                    type="file" 
                    onChange={(e) => handleFileChange(e, 'headPhoto')} 
                    className="w-full p-2 border rounded"
                    accept="image/*"
                  />
                  {typeof formData.headPhoto === 'string' && formData.headPhoto && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">Current photo available</p>
                    </div>
                  )}
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Baptism Date</label>
                  <input 
                    type="date" 
                    name="headBaptism" 
                    value={formData.headBaptism} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Holy Communion Date</label>
                  <input 
                    type="date" 
                    name="headHC" 
                    value={formData.headHC} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Uruthipoosal Date</label>
                  <input 
                    type="date" 
                    name="headUruthipoosal" 
                    value={formData.headUruthipoosal} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Marriage Date</label>
                  <input 
                    type="date" 
                    name="headMarriage" 
                    value={formData.headMarriage} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
              </div>
            </section>
            
            <section>
              <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm">
                <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
                  <Heart className="mr-2 h-5 w-5" /> Spouse Details
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Spouse Name</label>
                  <input 
                    name="spouseName" 
                    value={formData.spouseName} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Spouse's full name"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Date of Birth</label>
                  <input 
                    type="date" 
                    name="spouseDOB" 
                    value={formData.spouseDOB} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Photo</label>
                  <input 
                    type="file" 
                    onChange={(e) => handleFileChange(e, 'spousePhoto')} 
                    className="w-full p-2 border rounded"
                    accept="image/*"
                  />
                  {typeof formData.spousePhoto === 'string' && formData.spousePhoto && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">Current photo available</p>
                    </div>
                  )}
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Baptism Date</label>
                  <input 
                    type="date" 
                    name="spouseBaptism" 
                    value={formData.spouseBaptism} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Holy Communion Date</label>
                  <input 
                    type="date" 
                    name="spouseHC" 
                    value={formData.spouseHC} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Uruthipoosal Date</label>
                  <input 
                    type="date" 
                    name="spouseUruthipoosal" 
                    value={formData.spouseUruthipoosal} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  />
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Education</label>
                  <select 
                    name="spouseEducation" 
                    value={formData.spouseEducation} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select Education Level</option>
                    {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
                  </select>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Occupation</label>
                  <input 
                    name="spouseOccupation" 
                    value={formData.spouseOccupation} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Spouse's occupation"
                  />
                </div>
              </div>
            </section>
            
            <div className="mt-6 flex justify-between">
              <button 
                type="button" 
                onClick={prevStep}
                className="px-4 py-2 bg-gray-500 text-white rounded-md shadow-sm hover:bg-gray-600"
              >
                Previous
              </button>
              <button 
                type="submit" 
                className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700"
              >
                Next Step
              </button>
            </div>
          </motion.form>
        )}
        
        {/* Step 3: Family Members & Additional Info */}
        {currentStep === 3 && (
          <motion.form
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            onSubmit={handleSubmit}
          >
            <section className="mb-8">
              <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm flex justify-between items-center">
                <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
                  <Users className="mr-2 h-5 w-5" /> Family Members
                </h2>
                <button 
                  type="button" 
                  onClick={addMember}
                  className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm flex items-center"
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Member
                </button>
              </div>
              
              {formData.members.map((member, index) => (
                <div key={index} className="mb-6 p-4 border rounded-md bg-gray-50 relative">
                  <button 
                    type="button" 
                    onClick={() => removeMember(index)}
                    className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                  
                  <h3 className="font-medium mb-3">Member #{index + 1}</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Name</label>
                      <input 
                        name="name" 
                        value={member.name} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded" 
                        placeholder="Full name"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Relationship</label>
                      <select 
                        name="relationship" 
                        value={member.relationship} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select Relationship</option>
                        {relationshipOptions.map(r => <option key={r} value={r}>{r}</option>)}
                      </select>
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Date of Birth</label>
                      <input 
                        type="date" 
                        name="dob" 
                        value={member.dob as string} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Baptism Date</label>
                      <input 
                        type="date" 
                        name="baptism" 
                        value={member.baptism as string || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Holy Communion Date</label>
                      <input 
                        type="date" 
                        name="hc" 
                        value={member.hc as string || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Uruthipoosal Date</label>
                      <input 
                        type="date" 
                        name="uruthipoosal" 
                        value={member.uruthipoosal as string || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Marriage Date</label>
                      <input 
                        type="date" 
                        name="marriage" 
                        value={member.marriage as string || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Education</label>
                      <select 
                        name="education" 
                        value={member.education || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select Education Level</option>
                        {educationOptions.map(e => <option key={e} value={e}>{e}</option>)}
                      </select>
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Occupation</label>
                      <input 
                        name="occupation" 
                        value={member.occupation || ''} 
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded" 
                        placeholder="Occupation"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label className="block text-sm font-medium mb-1">Photo</label>
                      <input 
                        type="file" 
                        name="photo"
                        onChange={(e) => handleMemberChange(index, e)} 
                        className="w-full p-2 border rounded"
                        accept="image/*"
                      />
                      {typeof member.photo === 'string' && member.photo && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-500">Current photo available</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </section>
            
            <section>
              <div className="bg-indigo-50 p-3 rounded-md mb-4 shadow-sm">
                <h2 className="text-lg font-semibold text-indigo-700 flex items-center">
                  <BookOpen className="mr-2 h-5 w-5" /> Additional Information
                </h2>
              </div>
              
              <div className="grid grid-cols-1 gap-4">
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Notes</label>
                  <textarea 
                    name="notes" 
                    value={formData.notes} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Additional notes about the family"
                    rows={3}
                  ></textarea>
                </div>
                
                <div className="input-group">
                  <label className="block text-sm font-medium mb-1">Special Needs</label>
                  <textarea 
                    name="specialNeeds" 
                    value={formData.specialNeeds} 
                    onChange={handleChange} 
                    className="w-full p-2 border rounded" 
                    placeholder="Any special needs or requirements"
                    rows={3}
                  ></textarea>
                </div>
              </div>
            </section>
            
            <div className="mt-6 flex justify-between">
              <button 
                type="button" 
                onClick={prevStep}
                className="px-4 py-2 bg-gray-500 text-white rounded-md shadow-sm hover:bg-gray-600"
              >
                Previous
              </button>
              <button 
                type="submit" 
                disabled={isSaving}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" /> Save Changes
                  </>
                )}
              </button>
            </div>
          </motion.form>
        )}
      </div>
    </div>
  );
}
