'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  User, Users, Home, Calendar, Mail, Phone, ArrowLeft,
  MapPin, Briefcase, GraduationCap, Heart, BookOpen, DollarSign
} from 'lucide-react';
import { familiesApi } from '@/lib/api';
import ImageWithFallback from '@/components/ImageWithFallback';
import FamilyDuesSummary from '@/app/components/FamilyDuesSummary';

// Create an image loader function to handle backend image paths
const imageLoader = ({ src, width }: { src: string; width: number }) => {
  // Handle null or undefined src
  if (!src) {
    console.error('Image source is null or undefined');
    return '/placeholder-image.jpg'; // Return a placeholder image
  }

  // If the src is already a full URL, return it as is
  if (src.startsWith('http')) {
    console.log('Using full URL image source:', src);
    return src;
  }
  
  // Make sure src starts with a slash
  const normalizedSrc = src.startsWith('/') ? src : `/${src}`;
  
  // Check if API URL is defined
  if (!process.env.NEXT_PUBLIC_API_URL) {
    console.error('NEXT_PUBLIC_API_URL is not defined');
    return '/placeholder-image.jpg'; // Return placeholder as fallback
  }
  
  // Remove trailing slash from API URL if present
  const apiUrl = process.env.NEXT_PUBLIC_API_URL.endsWith('/') 
    ? process.env.NEXT_PUBLIC_API_URL.slice(0, -1) 
    : process.env.NEXT_PUBLIC_API_URL;
  
  // Construct the full image URL
  const imageUrl = `${apiUrl}${normalizedSrc}`;
  console.log('Constructed image URL:', imageUrl);
  return imageUrl;
};

// Define interfaces for our data types
interface Member {
  name: string;
  relationship: string;
  dob: string | Date;
  baptism?: string | Date;
  hc?: string | Date;
  uruthipoosal?: string | Date;
  marriage?: string | Date;
  education?: string;
  occupation?: string;
  photo?: string;
}

interface Family {
  _id: string;
  slNo: string;
  headName: string;
  gender: string;
  uraviam: string;
  houseNo: string;
  address: string;
  wardP: string;
  wardC: string;
  occupation: string;
  annualIncome: string;
  houseType: string;
  infrastructure: string;
  headDOB: string | Date;
  headBaptism?: string | Date;
  headHC?: string | Date;
  headUruthipoosal?: string | Date;
  headMarriage?: string | Date;
  headEducation: string;
  spouseName?: string;
  spouseDOB?: string | Date;
  spouseBaptism?: string | Date;
  spouseHC?: string | Date;
  spouseUruthipoosal?: string | Date;
  spouseOccupation?: string;
  spouseEducation?: string;
  members: Member[];
  headPhoto?: string;
  spousePhoto?: string;
  phone: string;
  email?: string;
  notes?: string;
  specialNeeds?: string;
}

export default function FamilyDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [family, setFamily] = useState<Family | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchFamilyDetails = async () => {
      try {
        setIsLoading(true);
        const data = await familiesApi.getById(params.id as string);
        setFamily(data);
      } catch (err) {
        console.error('Failed to fetch family details:', err);
        setError('Failed to load family details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchFamilyDetails();
    }
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={() => router.push('/admin/families')}
          className="bg-indigo-600 text-white px-4 py-2 rounded-md"
        >
          Back to Families
        </button>
      </div>
    );
  }

  if (!family) {
    return (
      <div className="text-center py-10">
        <div className="mb-4">Family not found</div>
        <button 
          onClick={() => router.push('/admin/families')}
          className="bg-indigo-600 text-white px-4 py-2 rounded-md"
        >
          Back to Families
        </button>
      </div>
    );
  }

  const formatDate = (dateString: string | Date | undefined): string => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="p-4 md:p-8 max-w-7xl mx-auto bg-gray-50 min-h-screen">
      {/* Header with actions */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <button 
            onClick={() => router.push('/admin/families')}
            className="mr-4 p-2 rounded-full bg-white shadow-sm hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>
          <h1 className="text-2xl font-bold text-gray-800">Family Details</h1>
        </div>
        <div className="flex space-x-3">
          <Link 
            href={`/admin/families/edit/${family._id}`}
            className="px-4 py-2 bg-yellow-500 text-white rounded-md shadow-sm hover:bg-yellow-600"
          >
            Edit Family
          </Link>
        </div>
      </div>

      {/* Family Overview Card */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="bg-indigo-600 p-4 text-white">
          <h2 className="text-xl font-semibold flex items-center">
            <Home className="mr-2" /> Family Overview
          </h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <User className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Family ID</p>
                <p className="font-medium">{family.slNo}</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <Phone className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Phone</p>
                <p className="font-medium">{family.phone || 'Not specified'}</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <Mail className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p className="font-medium">{family.email || 'Not specified'}</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <MapPin className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Address</p>
                <p className="font-medium">{family.address}</p>
                <p className="text-sm text-gray-500">House No: {family.houseNo}</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <Home className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">House Type</p>
                <p className="font-medium">{family.houseType}</p>
                <p className="text-sm text-gray-500">Infrastructure: {family.infrastructure}</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-indigo-100 p-2 rounded-md mr-3">
                <Users className="text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Ward</p>
                <p className="font-medium">Parish: {family.wardP}</p>
                <p className="text-sm">Church: {family.wardC}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Head of Family Card */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="bg-blue-600 p-4 text-white">
          <h2 className="text-xl font-semibold flex items-center">
            <User className="mr-2" /> Head of Family
          </h2>
        </div>
        <div className="p-6">
          <div className="flex flex-col md:flex-row">
            {family.headPhoto && (
              <div className="mb-4 md:mb-0 md:mr-6">
                <div className="w-32 h-32 rounded-lg overflow-hidden relative bg-gray-200">
                  <ImageWithFallback 
                    src={family.headPhoto}
                    alt={family.headName}
                    fill
                    sizes="(max-width: 768px) 100vw, 128px"
                    className="object-cover"
                  />
                </div>
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-xl font-semibold mb-3">{family.headName}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <Calendar className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Date of Birth</p>
                    <p className="font-medium">{formatDate(family.headDOB)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <BookOpen className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Baptism</p>
                    <p className="font-medium">{formatDate(family.headBaptism)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Heart className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Holy Communion</p>
                    <p className="font-medium">{formatDate(family.headHC)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <BookOpen className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Uruthipoosal</p>
                    <p className="font-medium">{formatDate(family.headUruthipoosal)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Heart className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Marriage</p>
                    <p className="font-medium">{formatDate(family.headMarriage)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <GraduationCap className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Education</p>
                    <p className="font-medium">{family.headEducation || 'Not specified'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Briefcase className="text-blue-500 mr-2 h-4 w-4" />
                  <div>
                    <p className="text-sm text-gray-500">Occupation</p>
                    <p className="font-medium">{family.occupation || 'Not specified'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Spouse Card (if exists) */}
      {family.spouseName && (
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
          <div className="bg-pink-600 p-4 text-white">
            <h2 className="text-xl font-semibold flex items-center">
              <User className="mr-2" /> Spouse
            </h2>
          </div>
          <div className="p-6">
            <div className="flex flex-col md:flex-row">
              {family.spousePhoto && (
                <div className="mb-4 md:mb-0 md:mr-6">
                  <div className="w-32 h-32 rounded-lg overflow-hidden relative">
                    <ImageWithFallback 
                      src={family.spousePhoto}
                      alt={family.spouseName || 'Spouse'}
                      fill
                      sizes="(max-width: 768px) 100vw, 128px"
                      className="object-cover"
                    />
                  </div>
                </div>
              )}
              <div className="flex-1">
                <h3 className="text-xl font-semibold mb-3">{family.spouseName}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="flex items-center">
                    <Calendar className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Date of Birth</p>
                      <p className="font-medium">{formatDate(family.spouseDOB)}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Baptism</p>
                      <p className="font-medium">{formatDate(family.spouseBaptism)}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Heart className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Holy Communion</p>
                      <p className="font-medium">{formatDate(family.spouseHC)}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Uruthipoosal</p>
                      <p className="font-medium">{formatDate(family.spouseUruthipoosal)}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <GraduationCap className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Education</p>
                      <p className="font-medium">{family.spouseEducation || 'Not specified'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Briefcase className="text-pink-500 mr-2 h-4 w-4" />
                    <div>
                      <p className="text-sm text-gray-500">Occupation</p>
                      <p className="font-medium">{family.spouseOccupation || 'Not specified'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Family Members Card */}
      {family.members && family.members.length > 0 && (
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
          <div className="bg-green-600 p-4 text-white">
            <h2 className="text-xl font-semibold flex items-center">
              <Users className="mr-2" /> Family Members ({family.members.length})
            </h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {family.members.map((member, index) => (
                <div key={index} className="border rounded-lg p-4 flex flex-col md:flex-row">
                  {member.photo && (
                    <div className="mb-4 md:mb-0 md:mr-4">
                      <div className="w-24 h-24 rounded-lg overflow-hidden relative">
                        <ImageWithFallback 
                          src={member.photo}
                          alt={member.name}
                          fill
                          sizes="(max-width: 768px) 100vw, 96px"
                          className="object-cover"
                        />
                      </div>
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-semibold">{member.name}</h3>
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                        {member.relationship}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="flex items-center">
                        <Calendar className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Date of Birth</p>
                          <p className="text-sm">{formatDate(member.dob)}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <BookOpen className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Baptism</p>
                          <p className="text-sm">{formatDate(member.baptism)}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Heart className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Holy Communion</p>
                          <p className="text-sm">{formatDate(member.hc)}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <BookOpen className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Uruthipoosal</p>
                          <p className="text-sm">{formatDate(member.uruthipoosal)}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Heart className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Marriage</p>
                          <p className="text-sm">{formatDate(member.marriage)}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <GraduationCap className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Education</p>
                          <p className="text-sm">{member.education || 'Not specified'}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Briefcase className="text-green-500 mr-2 h-4 w-4" />
                        <div>
                          <p className="text-xs text-gray-500">Occupation</p>
                          <p className="text-sm">{member.occupation || 'Not specified'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Payment Dues Card */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="bg-orange-600 p-4 text-white">
          <h2 className="text-xl font-semibold flex items-center">
            <DollarSign className="mr-2" /> Payment Dues & History
          </h2>
        </div>
        <div className="p-6">
          <FamilyDuesSummary familyId={family._id} showActions={true} />
        </div>
      </div>

      {/* Additional Information Card */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="bg-purple-600 p-4 text-white">
          <h2 className="text-xl font-semibold flex items-center">
            <Home className="mr-2" /> Additional Information
          </h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-lg mb-2">Family Notes</h3>
              <p className="text-gray-700">{family.notes || 'No additional notes.'}</p>
            </div>
            <div>
              <h3 className="font-semibold text-lg mb-2">Special Needs</h3>
              <p className="text-gray-700">{family.specialNeeds || 'None specified.'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions Footer */}
      <div className="flex justify-between mt-6">
        <button 
          onClick={() => router.push('/admin/families')}
          className="px-4 py-2 bg-gray-500 text-white rounded-md shadow-sm hover:bg-gray-600"
        >
          Back to Families
        </button>
        <div className="flex space-x-3">
          <Link 
            href={`/admin/families/edit/${family._id}`}
            className="px-4 py-2 bg-yellow-500 text-white rounded-md shadow-sm hover:bg-yellow-600"
          >
            Edit Family
          </Link>
        </div>
      </div>
    </div>
  );
}
