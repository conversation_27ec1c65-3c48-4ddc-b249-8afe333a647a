'use client';

import { useState, useEffect } from "react";
import { Users, DollarSign, Calendar, Activity, TrendingUp, ChevronRight, Bell, Clock, Heart, BookOpen } from "lucide-react";
import { useTheme } from "../context/ThemeContext";
import dynamic from 'next/dynamic';
import Link from 'next/link';

// Dynamically import chart components with no SSR to avoid hydration issues
const LineChart = dynamic(() => import('../components/charts/LineChart'), { ssr: false });
const PieChart = dynamic(() => import('../components/charts/PieChart'), { ssr: false });
const BarChart = dynamic(() => import('../components/charts/BarChart'), { ssr: false });

export default function AdminDashboard() {
  const { colors } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [animateStats, setAnimateStats] = useState(false);

  // Sample data for charts
  const offeringData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Monthly Offerings',
        data: [1200, 1900, 1500, 2500, 2100, 3000],
        borderColor: colors.primary,
        backgroundColor: `${colors.primary}20`,
        fill: true,
      }
    ]
  };

  const attendanceData = {
    labels: ['Sunday Service', 'Bible Study', 'Youth Group', 'Prayer Meeting', 'Choir Practice'],
    datasets: [
      {
        label: 'Attendance',
        data: [120, 45, 35, 60, 25],
        backgroundColor: [
          colors.primary,
          colors.secondary,
          colors.accent,
          '#4ade80',
          '#f97316'
        ],
        borderWidth: 1,
      }
    ]
  };

  const membershipData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'New Families',
        data: [5, 8, 3, 7, 4, 6],
        backgroundColor: colors.primary,
      }
    ]
  };

  // Recent activity data
  const recentActivities = [
    { id: 1, type: 'family', action: 'New family registered', name: 'Johnson Family', time: '2 hours ago', icon: <Users size={16} /> },
    { id: 2, type: 'offering', action: 'Donation received', name: '$250.00 from Smith Family', time: '5 hours ago', icon: <DollarSign size={16} /> },
    { id: 3, type: 'event', action: 'New event created', name: 'Sunday School Picnic', time: '1 day ago', icon: <Calendar size={16} /> },
    { id: 4, type: 'family', action: 'Family details updated', name: 'Williams Family', time: '2 days ago', icon: <Users size={16} /> },
  ];

  // Upcoming events
  const upcomingEvents = [
    { id: 1, name: 'Sunday Service', date: 'Sunday, 9:00 AM', attendees: 120 },
    { id: 2, name: 'Bible Study', date: 'Wednesday, 7:00 PM', attendees: 45 },
    { id: 3, name: 'Youth Group', date: 'Friday, 6:30 PM', attendees: 35 },
  ];

  useEffect(() => {
    setMounted(true);
    // Trigger animation after component mounts
    setTimeout(() => {
      setAnimateStats(true);
    }, 300);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration issues
  }

  return (
    <div className="animate-fadeIn">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Parish Dashboard</h1>
        <div className="flex items-center space-x-2">
          <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
            <Bell size={20} />
          </button>
          <button className="flex items-center space-x-2 bg-white p-2 rounded-lg shadow-sm border border-gray-100">
            <Clock size={16} />
            <span className="text-sm font-medium">Last updated: Just now</span>
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className={`card p-6 bg-white/80 backdrop-blur-md rounded-xl shadow-md border border-gray-100/50 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '100ms' }}>
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium opacity-70">Total Families</p>
              <p className="text-3xl font-bold mt-1 flex items-end">
                <span className="animate-counter">156</span>
                <span className="text-sm text-green-500 ml-2 flex items-center">
                  <TrendingUp size={14} className="mr-1" /> +12%
                </span>
              </p>
            </div>
            <div 
              className="p-3 rounded-full backdrop-blur-sm" 
              style={{ backgroundColor: `${colors.primary}30` }}
            >
              <Users size={20} style={{ color: colors.primary }} />
            </div>
          </div>
          <div className="mt-4 pt-4" style={{ borderTop: `1px solid ${colors.border}30` }}>
            <Link href="/admin/families" className="text-sm font-medium flex items-center" style={{ color: colors.primary }}>
              View all families <ChevronRight size={16} className="ml-1" />
            </Link>
          </div>
        </div>
        
        <div className={`card p-6 bg-white rounded-xl shadow-md border border-gray-100 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '200ms' }}>
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium opacity-70">Total Offerings</p>
              <p className="text-3xl font-bold mt-1 flex items-end">
                <span className="animate-counter">$12,450</span>
                <span className="text-sm text-green-500 ml-2 flex items-center">
                  <TrendingUp size={14} className="mr-1" /> +8%
                </span>
              </p>
            </div>
            <div 
              className="p-3 rounded-full" 
              style={{ backgroundColor: `${colors.secondary}20` }}
            >
              <DollarSign size={20} style={{ color: colors.secondary }} />
            </div>
          </div>
          <div className="mt-4 pt-4" style={{ borderTop: `1px solid ${colors.border}` }}>
            <Link href="/admin/offerings" className="text-sm font-medium flex items-center" style={{ color: colors.secondary }}>
              View all offerings <ChevronRight size={16} className="ml-1" />
            </Link>
          </div>
        </div>
        
        <div className={`card p-6 bg-white rounded-xl shadow-md border border-gray-100 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '300ms' }}>
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium opacity-70">Upcoming Events</p>
              <p className="text-3xl font-bold mt-1">
                <span className="animate-counter">8</span>
              </p>
            </div>
            <div 
              className="p-3 rounded-full" 
              style={{ backgroundColor: `${colors.accent}20` }}
            >
              <Calendar size={20} style={{ color: colors.accent }} />
            </div>
          </div>
          <div className="mt-4 pt-4" style={{ borderTop: `1px solid ${colors.border}` }}>
            <Link href="/admin/events" className="text-sm font-medium flex items-center" style={{ color: colors.accent }}>
              View all events <ChevronRight size={16} className="ml-1" />
            </Link>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className={`lg:col-span-2 bg-white/80 backdrop-blur-md p-6 rounded-xl shadow-md border border-gray-100/50 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '400ms' }}>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Offering Trends</h2>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-sm rounded-md bg-blue-50/80 backdrop-blur-sm text-blue-600">Monthly</button>
              <button className="px-3 py-1 text-sm rounded-md text-gray-500 hover:bg-gray-100/80 hover:backdrop-blur-sm">Quarterly</button>
              <button className="px-3 py-1 text-sm rounded-md text-gray-500 hover:bg-gray-100/80 hover:backdrop-blur-sm">Yearly</button>
            </div>
          </div>
          <div className="h-64">
            <LineChart data={offeringData} />
          </div>
        </div>
        
        <div className={`bg-white p-6 rounded-xl shadow-md border border-gray-100 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '500ms' }}>
          <h2 className="text-xl font-semibold mb-6">Attendance Breakdown</h2>
          <div className="h-64 flex items-center justify-center">
            <PieChart data={attendanceData} />
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className={`bg-white p-6 rounded-xl shadow-md border border-gray-100 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '600ms' }}>
          <h2 className="text-xl font-semibold mb-6">New Family Registrations</h2>
          <div className="h-64">
            <BarChart data={membershipData} />
          </div>
        </div>
        
        <div className={`lg:col-span-2 bg-white/80 backdrop-blur-md p-6 rounded-xl shadow-md border border-gray-100/50 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '700ms' }}>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <div 
              className="p-2 rounded-full backdrop-blur-sm" 
              style={{ backgroundColor: `${colors.primary}30` }}
            >
              <Activity size={18} style={{ color: colors.primary }} />
            </div>
          </div>
          
          {recentActivities.length > 0 ? (
            <div className="space-y-4">
              {recentActivities.map(activity => (
                <div key={activity.id} className="flex items-start p-3 rounded-lg hover:bg-white/60 hover:backdrop-blur-sm transition-all">
                  <div className="p-2 rounded-full mr-3 backdrop-blur-sm" style={{ 
                    backgroundColor: activity.type === 'family' ? `${colors.primary}30` : 
                                    activity.type === 'offering' ? `${colors.secondary}30` : 
                                    `${colors.accent}30` 
                  }}>
                    {activity.icon}
                  </div>
                  <div>
                    <p className="font-medium">{activity.action}</p>
                    <p className="text-sm opacity-70">{activity.name}</p>
                    <p className="text-xs opacity-50 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center p-8 text-center">
              <div>
                <Activity size={48} className="mx-auto mb-4 opacity-30" />
                <p className="text-lg">No recent activity to display.</p>
                <p className="opacity-75 mt-2">Activity will appear here as you use the system.</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className={`bg-white p-6 rounded-xl shadow-md border border-gray-100 transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '800ms' }}>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Upcoming Events</h2>
          <a href="/admin/events/add" className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
            Add New Event
          </a>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {upcomingEvents.map(event => (
            <div key={event.id} className="p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-3">
                <h3 className="font-medium">{event.name}</h3>
                <div className="p-1 rounded-full bg-blue-50">
                  <Calendar size={16} className="text-blue-600" />
                </div>
              </div>
              <p className="text-sm opacity-70 mb-3">{event.date}</p>
              <div className="flex items-center text-sm">
                <Users size={14} className="mr-1 opacity-70" />
                <span>{event.attendees} expected attendees</span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div className={`bg-gradient-to-r from-blue-500 to-indigo-600 p-6 rounded-xl shadow-md text-white transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '900ms' }}>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Spiritual Growth</h2>
            <div className="p-2 rounded-full bg-white bg-opacity-20">
              <Heart size={18} className="text-white" />
            </div>
          </div>
          <p className="mb-4 opacity-90">Track spiritual development and engagement in your parish</p>
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="bg-white bg-opacity-10 p-3 rounded-lg">
              <p className="text-sm opacity-80">Bible Studies</p>
              <p className="text-2xl font-bold">12</p>
            </div>
            <div className="bg-white bg-opacity-10 p-3 rounded-lg">
              <p className="text-sm opacity-80">Prayer Groups</p>
              <p className="text-2xl font-bold">8</p>
            </div>
          </div>
        </div>
        
        <div className={`bg-gradient-to-r from-amber-500 to-orange-600 p-6 rounded-xl shadow-md text-white transform transition-all duration-500 ${animateStats ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`} style={{ transitionDelay: '1000ms' }}>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Community Outreach</h2>
            <div className="p-2 rounded-full bg-white bg-opacity-20">
              <BookOpen size={18} className="text-white" />
            </div>
          </div>
          <p className="mb-4 opacity-90">Monitor your parish's impact in the community</p>
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="bg-white bg-opacity-10 p-3 rounded-lg">
              <p className="text-sm opacity-80">Charity Events</p>
              <p className="text-2xl font-bold">5</p>
            </div>
            <div className="bg-white bg-opacity-10 p-3 rounded-lg">
              <p className="text-sm opacity-80">Volunteers</p>
              <p className="text-2xl font-bold">42</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
