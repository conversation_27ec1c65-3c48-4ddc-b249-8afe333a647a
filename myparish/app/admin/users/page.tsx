'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { UserPlus, Search, Edit, Trash2, UserCheck, UserX, Loader2 } from 'lucide-react';
import { usersApi } from '../../../lib/api';

// Define User type
interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  status: string;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Use a ref to track if data has been fetched
  const dataFetched = useRef(false);
  
  // Memoize fetchUsers to prevent recreation on each render
  const fetchUsers = useCallback(async () => {
    if (dataFetched.current) return;
    
    console.log('Fetching users data');
    try {
      setIsLoading(true);
      const data = await usersApi.getAll();
      console.log('Fetched users data:', data);
      setUsers(Array.isArray(data) ? data : []);
      setError('');
      dataFetched.current = true;
    } catch (err) {
      console.error('Failed to fetch users:', err);
      setError('Failed to load users. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Use a ref to track if the effect has run
  useEffect(() => {
    fetchUsers();
    
    // Cleanup function
    return () => {
      console.log('AdminUsersPage unmounting');
    };
  }, [fetchUsers]);
  
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleStatusToggle = async (id: string) => {
    try {
      const user = users.find(u => u._id === id);
      if (!user) return;
      
      const newStatus = user.status === 'Active' ? 'Inactive' : 'Active';
      
      await usersApi.update(id, { status: newStatus });
      
      // Update local state
      setUsers(users.map(user => 
        user._id === id 
          ? { ...user, status: newStatus } 
          : user
      ));
    } catch (err) {
      console.error('Failed to update user status:', err);
      alert('Failed to update user status. Please try again.');
    }
  };
  
  const handleDeleteUser = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await usersApi.delete(id);
        setUsers(users.filter(user => user._id !== id));
      } catch (err) {
        console.error('Failed to delete user:', err);
        alert('Failed to delete user. Please try again.');
      }
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Manage Users</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold">User Accounts</h2>
            <p className="text-gray-500">Manage user access and permissions</p>
          </div>
          <a href="/admin/users/add" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center">
            <UserPlus size={18} className="mr-2" />
            Add New User
          </a>
        </div>
        
        <div className="mb-4 relative">
          <input 
            type="text" 
            placeholder="Search users..." 
            className="w-full p-2 pl-10 border rounded"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 size={24} className="animate-spin text-blue-600 mr-2" />
            <span>Loading users...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-600">
            {error}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 text-left">Name</th>
                  <th className="py-2 px-4 text-left">Email</th>
                  <th className="py-2 px-4 text-left">Role</th>
                  <th className="py-2 px-4 text-left">Status</th>
                  <th className="py-2 px-4 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map(user => (
                    <tr key={user._id} className="border-t">
                      <td className="py-3 px-4">{user.name}</td>
                      <td className="py-3 px-4">{user.email}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.role === 'Admin' 
                            ? 'bg-purple-100 text-purple-800' 
                            : user.role === 'Editor'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.status === 'Active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button 
                            className="p-1 rounded hover:bg-gray-100"
                            title="Edit user"
                            onClick={() => window.location.href = `/admin/users/edit/${user._id}`}
                          >
                            <Edit size={18} className="text-blue-600" />
                          </button>
                          <button 
                            className="p-1 rounded hover:bg-gray-100"
                            title={user.status === 'Active' ? 'Deactivate user' : 'Activate user'}
                            onClick={() => handleStatusToggle(user._id)}
                          >
                            {user.status === 'Active' ? (
                              <UserX size={18} className="text-orange-600" />
                            ) : (
                              <UserCheck size={18} className="text-green-600" />
                            )}
                          </button>
                          <button 
                            className="p-1 rounded hover:bg-gray-100"
                            title="Delete user"
                            onClick={() => handleDeleteUser(user._id)}
                          >
                            <Trash2 size={18} className="text-red-600" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td className="py-4 px-4 text-center" colSpan={5}>
                      No users found matching your search.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
