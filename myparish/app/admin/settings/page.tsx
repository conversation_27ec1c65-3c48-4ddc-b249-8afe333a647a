'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { 
  Save, 
  RotateCcw, 
  Upload, 
  X, 
  Image as ImageIcon, 
  Globe, 
  Palette, 
  Database, 
  Shield,
  Users,
  ChevronLeft
} from 'lucide-react';
import Image from 'next/image';

export default function AdminSettingsPage() {
  const { colors, updateColor, resetColors } = useTheme();
  const [activeSettingGroup, setActiveSettingGroup] = useState('');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  
  const [formData, setFormData] = useState({
    parishName: 'St. Mary\'s Parish',
    parishAddress: '123 Main Street, Anytown, USA',
    parishPhone: '(*************',
    parishEmail: '<EMAIL>',
    parishWebsite: 'www.stmarysparish.org',
    emailNotifications: true,
    backupFrequency: 'daily',
  });

  // State for parish images
  const [parishImages, setParishImages] = useState({
    logo: null as File | null,
    banner: null as File | null,
    church: null as File | null,
    gallery: [] as File[]
  });

  // Preview URLs for images
  const [previews, setPreviews] = useState({
    logo: '',
    banner: '',
    church: '',
    gallery: [] as string[]
  });

  // Settings cards data
  const settingsCards = [
    { 
      id: 'general', 
      title: 'General Settings', 
      description: 'Basic parish information and contact details',
      icon: <Globe size={24} />,
      color: colors.primary
    },
    { 
      id: 'appearance', 
      title: 'Appearance', 
      description: 'Customize theme colors and layout',
      icon: <Palette size={24} />,
      color: colors.secondary
    },
    { 
      id: 'media', 
      title: 'Media', 
      description: 'Manage parish images and gallery',
      icon: <ImageIcon size={24} />,
      color: colors.accent
    },
    { 
      id: 'users', 
      title: 'User Management', 
      description: 'Manage user roles and permissions',
      icon: <Users size={24} />,
      color: '#4ade80' // green-400
    },
    { 
      id: 'system', 
      title: 'System Settings', 
      description: 'Configure backups and notifications',
      icon: <Database size={24} />,
      color: '#f97316' // orange-500
    },
    { 
      id: 'security', 
      title: 'Security', 
      description: 'Authentication and data protection',
      icon: <Shield size={24} />,
      color: '#8b5cf6' // violet-500
    }
  ];

  // Open drawer when a setting group is selected
  useEffect(() => {
    if (activeSettingGroup) {
      setIsDrawerOpen(true);
    }
  }, [activeSettingGroup]);

  const handleCardClick = (id: string) => {
    setActiveSettingGroup(id);
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, type: 'logo' | 'banner' | 'church' | 'gallery') => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    if (type === 'gallery') {
      const newFiles = Array.from(e.target.files);
      setParishImages(prev => ({
        ...prev,
        gallery: [...prev.gallery, ...newFiles]
      }));
      
      // Create preview URLs for gallery images
      const newPreviews = newFiles.map(file => URL.createObjectURL(file));
      setPreviews(prev => ({
        ...prev,
        gallery: [...prev.gallery, ...newPreviews]
      }));
    } else {
      const file = e.target.files[0];
      setParishImages(prev => ({
        ...prev,
        [type]: file
      }));
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setPreviews(prev => ({
        ...prev,
        [type]: previewUrl
      }));
    }
    
    // Reset the input value to allow uploading the same file again
    e.target.value = '';
  };

  const removeImage = (type: 'logo' | 'banner' | 'church' | 'gallery', index?: number) => {
    if (type === 'gallery' && typeof index === 'number') {
      // Remove gallery image at index
      const newGallery = [...parishImages.gallery];
      newGallery.splice(index, 1);
      setParishImages(prev => ({
        ...prev,
        gallery: newGallery
      }));
      
      // Remove preview URL
      const newPreviews = [...previews.gallery];
      URL.revokeObjectURL(newPreviews[index]); // Free up memory
      newPreviews.splice(index, 1);
      setPreviews(prev => ({
        ...prev,
        gallery: newPreviews
      }));
    } else {
      // Remove single image
      setParishImages(prev => ({
        ...prev,
        [type]: null
      }));
      
      // Revoke preview URL to free up memory
      if (typeof previews[type] === 'string' && previews[type]) {
        URL.revokeObjectURL(previews[type]);
      }
      setPreviews(prev => ({
        ...prev,
        [type]: ''
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Settings saved:', formData);
    console.log('Parish images:', parishImages);
    // Here you would typically save the settings and upload images to your backend
    alert('Settings saved successfully!');
  };

  // Find the active card
  const activeCard = settingsCards.find(card => card.id === activeSettingGroup);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>
      
      {/* Settings Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {settingsCards.map(card => (
          <div 
            key={card.id}
            className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all cursor-pointer transform hover:-translate-y-1"
            onClick={() => handleCardClick(card.id)}
            style={{
              borderTopWidth: '4px',
              borderTopStyle: 'solid',
              borderTopColor: card.color,
              borderLeftColor: activeSettingGroup === card.id ? card.color : 'transparent',
              borderRightColor: activeSettingGroup === card.id ? card.color : 'transparent',
              borderBottomColor: activeSettingGroup === card.id ? card.color : 'transparent',
              boxShadow: activeSettingGroup === card.id ? `0 0 0 2px ${card.color}30` : undefined
            }}
          >
            <div className="flex items-start">
              <div 
                className="p-3 rounded-lg mr-4"
                style={{ backgroundColor: `${card.color}15`, color: card.color }}
              >
                {card.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-1">{card.title}</h3>
                <p className="text-sm text-gray-500">{card.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Settings Drawer */}
      <div 
        className={`fixed inset-y-0 right-0 z-50 w-full md:w-1/2 lg:w-1/3 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
          isDrawerOpen ? 'translate-x-0' : 'translate-x-full'
        } overflow-y-auto`}
        style={{ borderLeft: `1px solid ${colors.border}` }}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              {activeCard && (
                <>
                  <div 
                    className="p-2 rounded-lg mr-3"
                    style={{ backgroundColor: `${activeCard.color}15`, color: activeCard.color }}
                  >
                    {activeCard.icon}
                  </div>
                  <h2 className="text-xl font-semibold">{activeCard?.title}</h2>
                </>
              )}
            </div>
            <button 
              onClick={closeDrawer}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronLeft size={24} />
            </button>
          </div>

          {/* Drawer Content based on active setting */}
          {activeSettingGroup === 'general' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium mb-4">Parish Information</h3>
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Parish Name</label>
                    <input
                      type="text"
                      name="parishName"
                      value={formData.parishName}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Address</label>
                    <textarea
                      name="parishAddress"
                      value={formData.parishAddress}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                      rows={2}
                    ></textarea>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Phone</label>
                    <input
                      type="text"
                      name="parishPhone"
                      value={formData.parishPhone}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Email</label>
                    <input
                      type="email"
                      name="parishEmail"
                      value={formData.parishEmail}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Website</label>
                    <input
                      type="text"
                      name="parishWebsite"
                      value={formData.parishWebsite}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  
                  <div className="pt-4">
                    <button
                      type="submit"
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                    >
                      <Save size={18} className="mr-2" />
                      Save Information
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}
          
          {activeSettingGroup === 'appearance' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium mb-4">Theme Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Primary Color</label>
                  <input
                    type="color"
                    value={colors.primary}
                    onChange={(e) => updateColor('primary', e.target.value)}
                    className="w-full h-10 p-1 border rounded"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Secondary Color</label>
                  <input
                    type="color"
                    value={colors.secondary}
                    onChange={(e) => updateColor('secondary', e.target.value)}
                    className="w-full h-10 p-1 border rounded"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Accent Color</label>
                  <input
                    type="color"
                    value={colors.accent}
                    onChange={(e) => updateColor('accent', e.target.value)}
                    className="w-full h-10 p-1 border rounded"
                  />
                </div>
                
                <div className="pt-4">
                  <button
                    type="button"
                    onClick={resetColors}
                    className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 flex items-center"
                  >
                    <RotateCcw size={18} className="mr-2" />
                    Reset to Default
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeSettingGroup === 'media' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium mb-4">Parish Images</h3>
              
              {/* Parish Logo */}
              <div>
                <label className="block text-sm font-medium mb-2">Parish Logo</label>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {previews.logo ? (
                      <div className="relative w-32 h-32 border rounded-lg overflow-hidden">
                        <Image 
                          src={previews.logo} 
                          alt="Parish Logo" 
                          fill 
                          className="object-cover" 
                        />
                        <button 
                          type="button"
                          onClick={() => removeImage('logo')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    ) : (
                      <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <ImageIcon size={32} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow">
                    <p className="text-sm text-gray-600 mb-2">Upload your parish logo (recommended size: 200x200px)</p>
                    <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 cursor-pointer">
                      <Upload size={16} className="mr-2" />
                      <span>Upload Logo</span>
                      <input 
                        type="file" 
                        className="hidden" 
                        accept="image/*"
                        onChange={(e) => handleImageUpload(e, 'logo')}
                      />
                    </label>
                  </div>
                </div>
              </div>

              {/* Parish Banner */}
              <div>
                <label className="block text-sm font-medium mb-2">Parish Banner</label>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {previews.banner ? (
                      <div className="relative w-64 h-24 border rounded-lg overflow-hidden">
                        <Image 
                          src={previews.banner} 
                          alt="Parish Banner" 
                          fill 
                          className="object-cover" 
                        />
                        <button 
                          type="button"
                          onClick={() => removeImage('banner')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    ) : (
                      <div className="w-64 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <ImageIcon size={32} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow">
                    <p className="text-sm text-gray-600 mb-2">Upload a banner image (recommended size: 1200x400px)</p>
                    <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 cursor-pointer">
                      <Upload size={16} className="mr-2" />
                      <span>Upload Banner</span>
                      <input 
                        type="file" 
                        className="hidden" 
                        accept="image/*"
                        onChange={(e) => handleImageUpload(e, 'banner')}
                      />
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeSettingGroup === 'system' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium mb-4">System Settings</h3>
              <form>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Email Notifications</label>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="emailNotifications"
                        checked={formData.emailNotifications}
                        onChange={handleChange}
                        className="mr-2"
                      />
                      <span>Send email notifications for new events and offerings</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Database Backup Frequency</label>
                    <select
                      name="backupFrequency"
                      value={formData.backupFrequency}
                      onChange={handleChange}
                      className="w-full p-2 border rounded"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                  
                  <div className="pt-4">
                    <button
                      type="button"
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
                    >
                      <Save size={18} className="mr-2" />
                      Save Settings
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}
          
          {activeSettingGroup === 'users' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium mb-4">User Management</h3>
              <p className="text-gray-600">This section is under development.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
