'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { paymentsApi, familiesApi, paymentTypesApi } from '@/lib/api';

import { Loader2, Save, ArrowLeft, Calendar, DollarSign, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface PaymentType {
  _id: string;
  name: string;
  amount: number;
  frequency: string;
  startYear?: number;
  endYear?: number;
  startMonth?: number;
  endMonth?: number;
}



interface PaymentDue {
  _id: string;
  familyId: string;
  paymentTypeId: {
    _id: string;
    name: string;
  };
  year: number;
  period?: string;
  amount: number;
  isPaid: boolean;
  partialPayment: number;
  dueDate: string;
}

export default function NewPaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [families, setFamilies] = useState<Family[]>([]);
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [pendingDues, setPendingDues] = useState<PaymentDue[]>([]);
  const [selectedDue, setSelectedDue] = useState<PaymentDue | null>(null);
  const [isPartialPayment, setIsPartialPayment] = useState(false);
  const [relatedDues, setRelatedDues] = useState<PaymentDue[]>([]);
  const [dueDate, setDueDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  
  const generateReceiptNumber = () => {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `RCP-${year}${month}${day}-${random}`;
  };

  const [formData, setFormData] = useState({
    familyId: '',
    paymentTypeId: '',
    amount: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    year: new Date().getFullYear(),
    period: '',
    receiptNumber: generateReceiptNumber(),
    paymentMethod: 'cash',
    notes: ''
  });

  useEffect(() => {
    Promise.all([
      fetchFamilies(),
      fetchPaymentTypes()
    ]).then(() => {
      setIsLoading(false);

      // Handle URL parameters for pre-selection
      const familyId = searchParams.get('familyId');
      const dueId = searchParams.get('dueId');

      if (familyId) {
        setFormData(prev => ({ ...prev, familyId }));
        fetchPendingDues(familyId);

        // If a specific due is requested, select it after dues are loaded
        if (dueId) {
          setTimeout(() => {
            // This will be handled in the next useEffect when pendingDues updates
          }, 100);
        }
      }
    });
  }, [searchParams]);

  const fetchFamilies = async () => {
    try {
      const data = await familiesApi.getAll();
      setFamilies(data);
    } catch (error) {
      console.error('Failed to fetch families:', error);
    }
  };

  const fetchPaymentTypes = async () => {
    try {
      const data = await paymentTypesApi.getAll();
      setPaymentTypes(data);
    } catch (error) {
      console.error('Failed to fetch payment types:', error);
    }
  };



  const fetchPendingDues = async (familyId: string) => {
    try {
      const data = await paymentsApi.getPendingByFamily(familyId);
      setPendingDues(data);
    } catch (error) {
      console.error('Failed to fetch pending dues:', error);
      setPendingDues([]);
    }
  };

  const fetchRelatedDues = async (familyId: string, paymentTypeId: string) => {
    try {
      const selectedType = paymentTypes.find(type => type._id === paymentTypeId);
      if (!selectedType) return;

      // Only fetch related dues for payment types with date ranges
      if (selectedType.frequency === 'yearly' || selectedType.frequency === 'monthly') {
        const params: any = {};

        if (selectedType.startYear) params.startYear = selectedType.startYear;
        if (selectedType.endYear) params.endYear = selectedType.endYear;
        if (selectedType.startMonth) params.startMonth = selectedType.startMonth;
        if (selectedType.endMonth) params.endMonth = selectedType.endMonth;

        const data = await paymentsApi.getRelatedDues(familyId, paymentTypeId, params);
        setRelatedDues(data);
      } else {
        setRelatedDues([]);
      }
    } catch (error) {
      console.error('Failed to fetch related dues:', error);
      setRelatedDues([]);
    }
  };

  // Handle pre-selection of due from URL parameters
  useEffect(() => {
    const dueId = searchParams.get('dueId');
    if (dueId && pendingDues.length > 0) {
      const targetDue = pendingDues.find(due => due._id === dueId);
      if (targetDue) {
        handleDueSelection(targetDue);
      }
    }
  }, [pendingDues, searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'familyId' && value) {
      fetchPendingDues(value);
      setSelectedDue(null);
      setFormData({
        ...formData,
        familyId: value,
        paymentTypeId: '',
        amount: 0,
        year: new Date().getFullYear(),
        period: ''
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handlePaymentTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const typeId = e.target.value;
    const selectedType = paymentTypes.find(type => type._id === typeId);

    if (selectedType) {
      setFormData({
        ...formData,
        paymentTypeId: typeId,
        amount: selectedType.amount
      });

      // Fetch related dues if family is selected
      if (formData.familyId) {
        fetchRelatedDues(formData.familyId, typeId);
      }

      // Reset selected due when payment type changes
      setSelectedDue(null);
      setIsPartialPayment(false);
    } else {
      setFormData({
        ...formData,
        paymentTypeId: '',
        amount: 0
      });
      setRelatedDues([]);
    }
  };

  const handleDueSelection = (due: PaymentDue) => {
    setSelectedDue(due);
    
    // Calculate remaining amount
    const remainingAmount = due.amount - due.partialPayment;
    
    setFormData({
      ...formData,
      paymentTypeId: due.paymentTypeId._id,
      amount: remainingAmount,
      year: due.year,
      period: due.period || '',
      notes: `Payment for ${due.paymentTypeId.name} - ${due.year}${due.period ? ` (${due.period})` : ''}`
    });
  };

  const handlePartialPaymentToggle = () => {
    setIsPartialPayment(!isPartialPayment);

    if (!isPartialPayment) {
      // When enabling partial payment, reset amount to allow user input
      setFormData({
        ...formData,
        amount: 0
      });
    } else {
      // When disabling partial payment, set amount back to full amount
      if (selectedDue) {
        // For selected due, use remaining amount
        const remainingAmount = selectedDue.amount - selectedDue.partialPayment;
        setFormData({
          ...formData,
          amount: remainingAmount
        });
      } else {
        // For new payment, use payment type amount
        const selectedType = paymentTypes.find(type => type._id === formData.paymentTypeId);
        if (selectedType) {
          setFormData({
            ...formData,
            amount: selectedType.amount
          });
        }
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.familyId) {
      toast.error('Please select a family');
      return;
    }

    if (!formData.paymentTypeId) {
      toast.error('Please select a payment type');
      return;
    }

    if (formData.amount <= 0) {
      toast.error('Amount must be greater than zero');
      return;
    }

    // Validate amount doesn't exceed remaining due amount
    if (selectedDue) {
      const remainingAmount = selectedDue.amount - selectedDue.partialPayment;
      if (formData.amount > remainingAmount) {
        toast.error(`Amount cannot exceed remaining due amount of ${formatCurrency(remainingAmount)}`);
        return;
      }
    }
    
    try {
      setIsSaving(true);

      // If a due is selected, use the record payment endpoint
      if (selectedDue) {
        const paymentData = {
          dueId: selectedDue._id,
          amount: formData.amount,
          paymentDate: formData.paymentDate,
          receiptNumber: formData.receiptNumber,
          paymentMethod: formData.paymentMethod,
          notes: formData.notes,
          isPartialPayment: formData.amount < (selectedDue.amount - selectedDue.partialPayment)
        };

        console.log('Recording payment for due:', paymentData);
        const newPayment = await paymentsApi.recordPayment(paymentData);

        toast.success('Payment recorded successfully');
        router.push(`/admin/payments/receipt/${newPayment._id}`);
        return;
      }

      // If it's a partial payment and not for an existing due, create a due for the remaining amount
      if (isPartialPayment && !selectedDue) {
        const selectedType = paymentTypes.find(type => type._id === formData.paymentTypeId);
        if (selectedType && formData.amount < selectedType.amount) {
          const remainingAmount = selectedType.amount - formData.amount;

          // Create payment with partial payment flag
          const partialPaymentData: any = {
            familyId: formData.familyId,
            paymentTypeId: formData.paymentTypeId,
            amount: formData.amount,
            paymentDate: formData.paymentDate,
            year: formData.year,
            receiptNumber: formData.receiptNumber,
            paymentMethod: formData.paymentMethod,
            notes: formData.notes,
            isPartialPayment: true,
            createDueForRemaining: true,
            remainingAmount: remainingAmount,
            dueDate: dueDate
          };

          // Only add period if it has a value
          if (formData.period && formData.period.trim() !== '') {
            partialPaymentData.period = formData.period;
          }

          console.log('Sending partial payment data:', partialPaymentData);
          const newPayment = await paymentsApi.create(partialPaymentData);

          toast.success('Payment recorded successfully and due created for remaining amount');
          router.push(`/admin/payments/receipt/${newPayment._id}`);
          return;
        }
      }

      // Regular payment processing (new payment not tied to a due)
      const finalPaymentData: any = {
        familyId: formData.familyId,
        paymentTypeId: formData.paymentTypeId,
        amount: formData.amount,
        paymentDate: formData.paymentDate,
        year: formData.year,
        receiptNumber: formData.receiptNumber,
        paymentMethod: formData.paymentMethod,
        notes: formData.notes
      };

      // Only add period if it has a value
      if (formData.period && formData.period.trim() !== '') {
        finalPaymentData.period = formData.period;
      }

      console.log('Sending payment data:', finalPaymentData);
      const newPayment = await paymentsApi.create(finalPaymentData);

      toast.success('Payment recorded successfully');
      router.push(`/admin/payments/receipt/${newPayment._id}`);
    } catch (error) {
      console.error('Failed to create payment:', error);
      toast.error('Failed to record payment');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Link href="/admin/payments" className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-bold">Record New Payment</h1>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="familyId">
              Family <span className="text-red-500">*</span>
            </label>
            <select
              id="familyId"
              name="familyId"
              value={formData.familyId}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Family</option>
              {families.map(family => (
                <option key={family._id} value={family._id}>
                  {family.slNo} - {family.headName}
                </option>
              ))}
            </select>
          </div>

          {formData.familyId && pendingDues.length > 0 && (
            <div className="md:col-span-2">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-blue-800 font-semibold text-lg flex items-center">
                    <AlertCircle size={20} className="mr-2" />
                    Outstanding Dues for this Family
                  </h3>
                  <div className="text-right">
                    <p className="text-sm text-blue-600">Total Outstanding</p>
                    <p className="text-xl font-bold text-blue-800">
                      {formatCurrency(
                        pendingDues.reduce((total, due) =>
                          total + (due.amount - due.partialPayment), 0
                        )
                      )}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  {pendingDues.map(due => {
                    const remainingAmount = due.amount - due.partialPayment;
                    const isOverdue = new Date(due.dueDate) < new Date();
                    const isSelected = selectedDue?._id === due._id;

                    return (
                      <div
                        key={due._id}
                        onClick={() => handleDueSelection(due)}
                        className={`p-4 rounded-lg border cursor-pointer transition-all ${
                          isSelected
                            ? 'bg-blue-100 border-blue-400 shadow-md'
                            : 'bg-white border-gray-200 hover:border-blue-300 hover:shadow-sm'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-semibold text-gray-900">
                                {due.paymentTypeId.name}
                              </h4>
                              {isOverdue && (
                                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-600 rounded-full">
                                  Overdue
                                </span>
                              )}
                              {due.partialPayment > 0 && (
                                <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-600 rounded-full">
                                  Partial Payment
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              {due.year} {due.period && `(${due.period})`} • Due: {formatDate(due.dueDate)}
                            </p>
                          </div>
                          {isSelected && (
                            <CheckCircle size={20} className="text-blue-600 flex-shrink-0" />
                          )}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500 font-medium">Total Amount</p>
                            <p className="text-gray-900 font-semibold">{formatCurrency(due.amount)}</p>
                          </div>

                          {due.partialPayment > 0 && (
                            <div>
                              <p className="text-gray-500 font-medium">Already Paid</p>
                              <p className="text-green-600 font-semibold">{formatCurrency(due.partialPayment)}</p>
                            </div>
                          )}

                          <div>
                            <p className="text-gray-500 font-medium">Remaining</p>
                            <p className={`font-semibold ${isOverdue ? 'text-red-600' : 'text-orange-600'}`}>
                              {formatCurrency(remainingAmount)}
                            </p>
                          </div>

                          <div>
                            <p className="text-gray-500 font-medium">Status</p>
                            <p className={`font-semibold ${isOverdue ? 'text-red-600' : 'text-blue-600'}`}>
                              {isOverdue ? 'Overdue' : 'Pending'}
                            </p>
                          </div>
                        </div>

                        {isSelected && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-200">
                            <p className="text-sm text-blue-800 font-medium">
                              ✓ Selected for payment: {formatCurrency(remainingAmount)}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 p-3 bg-white rounded-md border border-blue-200">
                  <p className="text-sm text-blue-700">
                    💡 <strong>Tip:</strong> Click on any due above to automatically fill the payment form,
                    or create a new payment using the form below.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentTypeId">
              Payment Type <span className="text-red-500">*</span>
            </label>
            <select
              id="paymentTypeId"
              name="paymentTypeId"
              value={formData.paymentTypeId}
              onChange={handlePaymentTypeChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              disabled={!!selectedDue}
            >
              <option value="">Select Payment Type</option>
              {paymentTypes.map(type => (
                <option key={type._id} value={type._id}>
                  {type.name} - {formatCurrency(type.amount)}
                </option>
              ))}
            </select>
          </div>

          {/* Related Dues Section for Multi-Period Payment Types */}
          {formData.familyId && formData.paymentTypeId && relatedDues.length > 0 && (
            <div className="md:col-span-2">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border border-green-200 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-green-800 font-semibold text-lg flex items-center">
                    <Calendar size={20} className="mr-2" />
                    Related Dues for Selected Payment Type
                  </h3>
                  <div className="text-right">
                    <p className="text-sm text-green-600">Total Related Dues</p>
                    <p className="text-xl font-bold text-green-800">
                      {formatCurrency(
                        relatedDues.reduce((total, due) =>
                          total + (due.amount - due.partialPayment), 0
                        )
                      )}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {relatedDues.map(due => {
                    const remainingAmount = due.amount - due.partialPayment;
                    const isOverdue = new Date(due.dueDate) < new Date();

                    return (
                      <div
                        key={due._id}
                        className="p-3 bg-white rounded-md border border-green-200 hover:border-green-300 transition-colors"
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {due.paymentTypeId.name} - {due.year}
                              {due.period && ` (${due.period})`}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Due: {formatDate(due.dueDate)}
                              {isOverdue && <span className="text-red-600 ml-2">• Overdue</span>}
                              {due.partialPayment > 0 && <span className="text-yellow-600 ml-2">• Partial Payment</span>}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">
                              {formatCurrency(remainingAmount)}
                            </p>
                            <p className="text-xs text-gray-500">
                              {due.partialPayment > 0 ? 'Remaining' : 'Total'}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 p-3 bg-white rounded-md border border-green-200">
                  <p className="text-sm text-green-700">
                    💡 <strong>Note:</strong> This payment type covers multiple periods.
                    All related dues for this payment type are shown above.
                    You can pay for individual periods by selecting them from the outstanding dues section.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="amount">
              Amount <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <DollarSign size={16} className="text-gray-500" />
              </div>
              <input
                type="number"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
                required
                min="0"
                step="0.01"
                disabled={!!selectedDue && !isPartialPayment}
                max={selectedDue ? (selectedDue.amount - selectedDue.partialPayment) : undefined}
              />
            </div>
          </div>

          {formData.paymentTypeId && (
            <div className="md:col-span-2">
              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-blue-600"
                  checked={isPartialPayment}
                  onChange={handlePartialPaymentToggle}
                />
                <span className="ml-2 text-gray-700">Make partial payment</span>
              </label>
              {isPartialPayment && !selectedDue && (
                <p className="text-sm text-gray-600 mt-1">
                  Enter the amount you wish to pay. The remaining balance will be created as a due.
                </p>
              )}
              {isPartialPayment && selectedDue && (
                <p className="text-sm text-gray-600 mt-1">
                  Enter the amount you wish to pay. The remaining balance will stay as an outstanding due.
                </p>
              )}
            </div>
          )}

          {isPartialPayment && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Due Date for Remaining Amount
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  name="dueDate"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                  required
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentDate">
              Payment Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar size={16} className="text-gray-500" />
              </div>
              <input
                type="date"
                id="paymentDate"
                name="paymentDate"
                value={formData.paymentDate}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="year">
              Year <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="year"
              name="year"
              value={formData.year}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
              min="2000"
              max="2100"
              disabled={!!selectedDue}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="period">
              Period
            </label>
            <select
              id="period"
              name="period"
              value={formData.period}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Period (Optional)</option>
              <option value="Q1">Q1</option>
              <option value="Q2">Q2</option>
              <option value="Q3">Q3</option>
              <option value="Q4">Q4</option>
              <option value="H1">H1 (First Half)</option>
              <option value="H2">H2 (Second Half)</option>
              <option value="Monthly">Monthly</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="receiptNumber">
              Receipt Number <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="receiptNumber"
              name="receiptNumber"
              value={formData.receiptNumber}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="paymentMethod">
              Payment Method <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <CreditCard size={16} className="text-gray-500" />
              </div>
              <select
                id="paymentMethod"
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                className="w-full pl-10 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="cash">Cash</option>
                <option value="check">Check</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="upi">UPI/Online Payment</option>
                <option value="card">Card</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-1" htmlFor="notes">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Add any additional notes here..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Link
            href="/admin/payments"
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 mr-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 size={18} className="animate-spin mr-2" />
            ) : (
              <Save size={18} className="mr-2" />
            )}
            Record Payment
          </button>
        </div>
      </form>
    </div>
  );
}
