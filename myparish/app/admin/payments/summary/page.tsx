'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paymentsApi, paymentTypesApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { 
  Loader2, Filter, Download, RefreshCw, Calendar,
  DollarSign, CheckCircle, XCircle, Clock, AlertTriangle,
  TrendingUp, TrendingDown, BarChart3, PieChart,
  ArrowLeft, Users, CreditCard
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface PaymentSummaryItem {
  paymentType: {
    _id: string;
    name: string;
  };
  dueCount: number;
  paidCount: number;
  pendingCount: number;
  overdueCount: number;
  partialCount: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
}

interface OverallSummary {
  totalDues: number;
  totalPaid: number;
  totalPending: number;
  totalOverdue: number;
  totalPartial: number;
  totalAmount: number;
  totalPaidAmount: number;
  totalPendingAmount: number;
  collectionRate: number;
}

export default function PaymentSummaryPage() {
  const router = useRouter();
  const { isDarkMode } = useTheme();
  
  const [summaryData, setSummaryData] = useState<PaymentSummaryItem[]>([]);
  const [overallSummary, setOverallSummary] = useState<OverallSummary>({
    totalDues: 0,
    totalPaid: 0,
    totalPending: 0,
    totalOverdue: 0,
    totalPartial: 0,
    totalAmount: 0,
    totalPaidAmount: 0,
    totalPendingAmount: 0,
    collectionRate: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchSummaryData();
  }, [selectedYear]);

  const fetchSummaryData = async () => {
    try {
      setIsLoading(true);
      const data = await paymentsApi.getSummaryByYear(selectedYear);
      setSummaryData(data);
      
      // Calculate overall summary
      const overall = data.reduce((acc: OverallSummary, item: PaymentSummaryItem) => ({
        totalDues: acc.totalDues + item.dueCount,
        totalPaid: acc.totalPaid + item.paidCount,
        totalPending: acc.totalPending + item.pendingCount,
        totalOverdue: acc.totalOverdue + item.overdueCount,
        totalPartial: acc.totalPartial + item.partialCount,
        totalAmount: acc.totalAmount + item.totalAmount,
        totalPaidAmount: acc.totalPaidAmount + item.paidAmount,
        totalPendingAmount: acc.totalPendingAmount + item.pendingAmount,
        collectionRate: 0 // Will calculate after
      }), {
        totalDues: 0,
        totalPaid: 0,
        totalPending: 0,
        totalOverdue: 0,
        totalPartial: 0,
        totalAmount: 0,
        totalPaidAmount: 0,
        totalPendingAmount: 0,
        collectionRate: 0
      });
      
      // Calculate collection rate
      overall.collectionRate = overall.totalAmount > 0 
        ? (overall.totalPaidAmount / overall.totalAmount) * 100 
        : 0;
      
      setOverallSummary(overall);
    } catch (error) {
      console.error('Failed to fetch payment summary:', error);
      toast.error('Failed to load payment summary');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: 'paid' | 'pending' | 'overdue' | 'partial') => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-50 border-green-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'overdue': return 'text-red-600 bg-red-50 border-red-200';
      case 'partial': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const exportSummary = () => {
    // Create CSV content
    const headers = [
      'Payment Type',
      'Total Dues',
      'Paid',
      'Pending',
      'Overdue',
      'Partial',
      'Total Amount',
      'Paid Amount',
      'Pending Amount',
      'Collection Rate %'
    ];
    
    const rows = summaryData.map(item => [
      item.paymentType.name,
      item.dueCount,
      item.paidCount,
      item.pendingCount,
      item.overdueCount,
      item.partialCount,
      item.totalAmount,
      item.paidAmount,
      item.pendingAmount,
      item.totalAmount > 0 ? ((item.paidAmount / item.totalAmount) * 100).toFixed(2) : '0'
    ]);
    
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment-summary-${selectedYear}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Summary exported successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/admin/payments"
                className={`p-2 rounded-lg ${isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Dues Summary
                </h1>
                <p className={`mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Outstanding payment dues and collection status for {selectedYear}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg border ${
                  isDarkMode 
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-800' 
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                } flex items-center space-x-2`}
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </button>
              
              <button
                onClick={exportSummary}
                className={`px-4 py-2 rounded-lg border ${
                  isDarkMode 
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-800' 
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                } flex items-center space-x-2`}
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
              
              <button
                onClick={fetchSummaryData}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className={`mb-6 p-4 rounded-lg border ${
            isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <div className="flex items-center space-x-4">
              <div>
                <label className={`block text-sm font-medium mb-1 ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Year
                </label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                  className={`px-3 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  {Array.from({ length: 10 }, (_, i) => {
                    const year = new Date().getFullYear() - 5 + i;
                    return (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Outstanding Dues Alert */}
        {overallSummary.totalPending > 0 && (
          <div className={`mb-6 p-4 rounded-lg border-l-4 border-red-500 ${
            isDarkMode ? 'bg-red-900/20 border-red-400' : 'bg-red-50'
          }`}>
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-red-300' : 'text-red-800'}`}>
                Outstanding Dues Alert
              </h3>
            </div>
            <p className={`mt-1 ${isDarkMode ? 'text-red-200' : 'text-red-700'}`}>
              There are <strong>{overallSummary.totalPending}</strong> unpaid dues totaling{' '}
              <strong>{formatCurrency(overallSummary.totalPendingAmount)}</strong> that require attention.
              {overallSummary.totalOverdue > 0 && (
                <span className="block mt-1">
                  <strong>{overallSummary.totalOverdue}</strong> of these are overdue.
                </span>
              )}
            </p>
          </div>
        )}

        {/* Overall Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`p-6 rounded-lg border ${
            isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Total Dues
                </p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {overallSummary.totalDues}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Amount: {formatCurrency(overallSummary.totalAmount)}
              </p>
            </div>
          </div>

          <div className={`p-6 rounded-lg border ${
            isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Paid Dues
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {overallSummary.totalPaid}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Amount: {formatCurrency(overallSummary.totalPaidAmount)}
              </p>
            </div>
          </div>

          <div className={`p-6 rounded-lg border ${
            isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Pending Dues
                </p>
                <p className="text-2xl font-bold text-yellow-600">
                  {overallSummary.totalPending}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Amount: {formatCurrency(overallSummary.totalPendingAmount)}
              </p>
            </div>
          </div>

          <div className={`p-6 rounded-lg border ${
            isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Collection Rate
                </p>
                <p className={`text-2xl font-bold ${
                  overallSummary.collectionRate >= 80 ? 'text-green-600' :
                  overallSummary.collectionRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {overallSummary.collectionRate.toFixed(1)}%
                </p>
              </div>
              <div className={`p-3 rounded-full ${
                overallSummary.collectionRate >= 80 ? 'bg-green-100' :
                overallSummary.collectionRate >= 60 ? 'bg-yellow-100' : 'bg-red-100'
              }`}>
                {overallSummary.collectionRate >= 80 ? (
                  <TrendingUp className="h-6 w-6 text-green-600" />
                ) : (
                  <TrendingDown className="h-6 w-6 text-red-600" />
                )}
              </div>
            </div>
            <div className="mt-4">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Overdue: {overallSummary.totalOverdue}
              </p>
            </div>
          </div>
        </div>

        {/* Detailed Summary Table */}
        <div className={`rounded-lg border ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Payment Type Summary
            </h2>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Detailed breakdown by payment type
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className={isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}>
                <tr>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Payment Type
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Total Dues
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Status Breakdown
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Amount Summary
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Collection Rate
                  </th>
                  <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                {summaryData.map((item) => {
                  const collectionRate = item.totalAmount > 0
                    ? (item.paidAmount / item.totalAmount) * 100
                    : 0;

                  return (
                    <tr key={item.paymentType._id} className={isDarkMode ? 'bg-gray-800' : 'bg-white'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-100 rounded-lg mr-3">
                            <CreditCard className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <div className={`text-sm font-medium ${
                              isDarkMode ? 'text-white' : 'text-gray-900'
                            }`}>
                              {item.paymentType.name}
                            </div>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.dueCount}
                        </div>
                        <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {formatCurrency(item.totalAmount)}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor('paid')}`}>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Paid: {item.paidCount}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor('pending')}`}>
                              <Clock className="h-3 w-3 mr-1" />
                              Pending: {item.pendingCount}
                            </span>
                            {item.overdueCount > 0 && (
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor('overdue')}`}>
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Overdue: {item.overdueCount}
                              </span>
                            )}
                          </div>
                          {item.partialCount > 0 && (
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor('partial')}`}>
                                <DollarSign className="h-3 w-3 mr-1" />
                                Partial: {item.partialCount}
                              </span>
                            </div>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            Paid: {formatCurrency(item.paidAmount)}
                          </div>
                          <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Pending: {formatCurrency(item.pendingAmount)}
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className={`h-2 rounded-full ${
                                collectionRate >= 80 ? 'bg-green-500' :
                                collectionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(collectionRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className={`text-sm font-medium ${
                            collectionRate >= 80 ? 'text-green-600' :
                            collectionRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {collectionRate.toFixed(1)}%
                          </span>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            href={`/admin/payments/dues?paymentTypeId=${item.paymentType._id}&year=${selectedYear}&status=unpaid`}
                            className="text-red-600 hover:text-red-900"
                          >
                            View Unpaid
                          </Link>
                          <Link
                            href={`/admin/payments/dues?paymentTypeId=${item.paymentType._id}&year=${selectedYear}&status=all`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            View All
                          </Link>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {summaryData.length === 0 && (
            <div className="text-center py-12">
              <BarChart3 className={`mx-auto h-12 w-12 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
              <h3 className={`mt-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-900'}`}>
                No payment data
              </h3>
              <p className={`mt-1 text-sm ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                No payment dues found for {selectedYear}.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
