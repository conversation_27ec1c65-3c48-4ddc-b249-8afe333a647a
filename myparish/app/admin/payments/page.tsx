'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { paymentsApi, familiesApi, paymentTypesApi } from '@/lib/api';
import { useTheme } from '@/app/context/ThemeContext';
import { 
  Loader2, Plus, Search, Filter, Calendar, DollarSign, 
  FileText, Download, ChevronLeft, ChevronRight, CheckCircle, XCircle,
  BarChart2, PieChart
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Payment {
  _id: string;
  familyId: {
    _id: string;
    headName: string;
    slNo: string;
  };
  paymentTypeId: {
    _id: string;
    name: string;
  };
  amount: number;
  paymentDate: string;
  year: number;
  period?: string;
  receiptNumber: string;
  paymentMethod: string;
  collectedBy: {
    _id: string;
    name: string;
  };
}

interface Family {
  _id: string;
  headName: string;
  slNo: string;
}

interface PaymentType {
  _id: string;
  name: string;
}

interface PaymentSummary {
  _id?: string;
  paymentType?: {
    _id: string;
    name: string;
  };
  totalAmount: number;
  count?: number;
  paidAmount?: number;
  pendingAmount?: number;
}

interface Pagination {
  total: number;
  page: number;
  pages: number;
}

export default function PaymentsPage() {
  const router = useRouter();
  const { colors } = useTheme();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [families, setFamilies] = useState<Family[]>([]);
  const [paymentTypes, setPaymentTypes] = useState<PaymentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    page: 1,
    pages: 1
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [summary, setSummary] = useState<PaymentSummary[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [viewMode, setViewMode] = useState<'list' | 'summary'>('list');
  
  // Filters
  const [filters, setFilters] = useState({
    familyId: '',
    paymentTypeId: '',
    year: new Date().getFullYear(),
    startDate: '',
    endDate: '',
    paymentMethod: ''
  });
  
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    Promise.all([
      fetchFamilies(),
      fetchPaymentTypes()
    ]).then(() => {
      fetchPayments();
      fetchSummary();
    });
  }, [currentPage]);

  const fetchPayments = async () => {
    try {
      setIsLoading(true);
      
      const params = {
        page: currentPage,
        limit: 10,
        ...filters
      };
      
      const data = await paymentsApi.getAll(params);
      
      // Handle the response format from the backend
      if (data.payments) {
        setPayments(data.payments);
        setPagination({
          total: data.pagination.total,
          page: data.pagination.page,
          pages: data.pagination.pages
        });
      } else {
        // Fallback for older API format
        setPayments(data);
        setPagination({
          total: data.length,
          page: 1,
          pages: 1
        });
      }
    } catch (error) {
      console.error('Failed to fetch payments:', error);
      toast.error('Failed to load payments');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSummary = async () => {
    try {
      const data = await paymentsApi.getSummaryByYear(filters.year);
      setSummary(Array.isArray(data) ? data : []);
      
      // Calculate total amount
      const total = Array.isArray(data) 
        ? data.reduce((sum: number, item: PaymentSummary) => sum + item.totalAmount, 0)
        : 0;
      setTotalAmount(total);
    } catch (error) {
      console.error('Failed to fetch payment summary:', error);
      setSummary([]);
      setTotalAmount(0);
    }
  };

  const fetchFamilies = async () => {
    try {
      const data = await familiesApi.getAll();
      setFamilies(data);
    } catch (error) {
      console.error('Failed to fetch families:', error);
    }
  };

  const fetchPaymentTypes = async () => {
    try {
      const data = await paymentTypesApi.getAll();
      setPaymentTypes(data);
    } catch (error) {
      console.error('Failed to fetch payment types:', error);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = () => {
    setCurrentPage(1);
    fetchPayments();
    fetchSummary();
  };

  const resetFilters = () => {
    setFilters({
      familyId: '',
      paymentTypeId: '',
      year: new Date().getFullYear(),
      startDate: '',
      endDate: '',
      paymentMethod: ''
    });
    setCurrentPage(1);
    fetchPayments();
    fetchSummary();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'Cash';
      case 'check': return 'Check';
      case 'bank_transfer': return 'Bank Transfer';
      case 'online': return 'Online';
      case 'other': return 'Other';
      default: return method;
    }
  };

  const exportPayments = () => {
    // Implementation for exporting payments data
    toast.info('Export functionality will be implemented soon');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Payments Dashboard</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode(viewMode === 'list' ? 'summary' : 'list')}
            className="bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 flex items-center"
          >
            {viewMode === 'list' ? (
              <>
                <BarChart2 size={18} className="mr-2" />
                View Summary
              </>
            ) : (
              <>
                <FileText size={18} className="mr-2" />
                View List
              </>
            )}
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 flex items-center"
          >
            <Filter size={18} className="mr-2" />
            Filters
          </button>
          <Link
            href="/admin/payments/summary"
            className="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 flex items-center"
          >
            <PieChart size={18} className="mr-2" />
            Dues Summary
          </Link>
          <Link
            href="/admin/payments/generate-dues"
            className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 flex items-center"
          >
            <Calendar size={18} className="mr-2" />
            Generate Dues
          </Link>
          <Link
            href="/admin/payments/new"
            className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 flex items-center"
          >
            <Plus size={18} className="mr-2" />
            New Payment
          </Link>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg shadow-md">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Family</label>
              <select
                name="familyId"
                value={filters.familyId}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Families</option>
                {families.map(family => (
                  <option key={family._id} value={family._id}>
                    {family.slNo} - {family.headName}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Payment Type</label>
              <select
                name="paymentTypeId"
                value={filters.paymentTypeId}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Types</option>
                {paymentTypes.map(type => (
                  <option key={type._id} value={type._id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Year</label>
              <input
                type="number"
                name="year"
                value={filters.year}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
                min="2000"
                max="2100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Start Date</label>
              <input
                type="date"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">End Date</label>
              <input
                type="date"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Payment Method</label>
              <select
                name="paymentMethod"
                value={filters.paymentMethod}
                onChange={handleFilterChange}
                className="w-full p-2 border rounded"
              >
                <option value="">All Methods</option>
                <option value="cash">Cash</option>
                <option value="check">Check</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="online">Online</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          <div className="flex justify-end mt-4 space-x-2">
            <button
              onClick={resetFilters}
              className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50"
            >
              Reset
            </button>
            <button
              onClick={applyFilters}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Summary View */}
      {viewMode === 'summary' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold">Payment Summary for {filters.year}</h2>
            <button
              onClick={exportPayments}
              className="bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 flex items-center"
            >
              <Download size={18} className="mr-2" />
              Export Data
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <h3 className="text-blue-800 text-sm font-medium mb-2">Total Collections</h3>
              <p className="text-2xl font-bold text-blue-900">{formatCurrency(totalAmount)}</p>
              <p className="text-sm text-blue-700 mt-1">{totalCount} payments</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
              <h3 className="text-green-800 text-sm font-medium mb-2">Most Common Payment</h3>
              <p className="text-2xl font-bold text-green-900">
                {summary.length > 0
                  ? (() => {
                      const paymentType = summary.reduce((prev, current) =>
                        ((current.count ?? 0) > (prev.count ?? 0)) ? current : prev
                      ).paymentType;
                      if (typeof paymentType === 'object' && paymentType !== null) {
                        return paymentType.name;
                      }
                      return paymentType || 'N/A';
                    })()
                  : 'N/A'}
              </p>
              <p className="text-sm text-green-700 mt-1">
                {summary.length > 0 ? summary.reduce((prev, current) => 
                  ((current.count ?? 0) > (prev.count ?? 0)) ? current : prev
                ).count : 0} payments
              </p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
              <h3 className="text-purple-800 text-sm font-medium mb-2">Highest Revenue</h3>
              <p className="text-2xl font-bold text-purple-900">
                {summary.length > 0 ? summary.reduce((prev, current) => 
                  ((current.totalAmount ?? 0) > (prev.totalAmount ?? 0)) ? current : prev
                ).paymentType?.name : 'N/A'}
              </p>
              <p className="text-sm text-purple-700 mt-1">
                {summary.length > 0 ? formatCurrency(summary.reduce((prev, current) => 
                  ((current.totalAmount ?? 0) > (prev.totalAmount ?? 0)) ? current : prev
                ).totalAmount || 0) : formatCurrency(0)}
              </p>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Type
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Count
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Amount
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    % of Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {summary.map(item => (
                  <tr key={item._id || item.paymentType?._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {typeof item.paymentType === 'object'
                          ? item.paymentType?.name
                          : item.paymentType || ''}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <span className="text-sm text-gray-900">
                        {item.count || item.paidAmount || 0}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(item.totalAmount || 0)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <span className="text-sm text-gray-900">
                        {totalAmount > 0 ? ((item.totalAmount || 0 / totalAmount) * 100).toFixed(1) : 0}%
                      </span>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${totalAmount > 0 ? (item.totalAmount / totalAmount) * 100 : 0}%` }}
                        ></div>
                      </div>
                    </td>
                  </tr>
                ))}
                
                {summary.length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                      No payment data available for the selected filters
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Payments Table */}
      {viewMode === 'list' && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Receipt #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Family
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.length > 0 ? (
                  payments.map(payment => (
                    <tr key={payment._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-gray-900">
                          {payment.receiptNumber}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {payment.familyId.headName}
                          </div>
                          <div className="ml-1 text-sm text-gray-500">
                            ({payment.familyId.slNo})
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {payment.paymentTypeId.name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(payment.amount)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-500">
                          {formatDate(payment.paymentDate)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-500">
                          {getPaymentMethodLabel(payment.paymentMethod)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            href={`/admin/payments/${payment._id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            View
                          </Link>
                          <Link
                            href={`/admin/payments/edit/${payment._id}`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            Edit
                          </Link>
                          <Link
                            href={`/admin/payments/receipt/${payment._id}`}
                            className="text-green-600 hover:text-green-900"
                          >
                            Receipt
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                      {isLoading ? (
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-5 w-5 animate-spin mr-2" />
                          Loading payments...
                        </div>
                      ) : (
                        'No payments found'
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === 1 
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.pages))}
                  disabled={currentPage === pagination.pages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === pagination.pages 
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{payments.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1 
                          ? 'text-gray-300 cursor-not-allowed' 
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>
                    
                    {Array.from({ length: pagination.pages }, (_, i) => i + 1).map(page => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`relative inline-flex items-center px-4 py-2 border ${
                          currentPage === page
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        } text-sm font-medium`}
                      >
                        {page}
                      </button>
                    ))}
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.pages))}
                      disabled={currentPage === pagination.pages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === pagination.pages 
                          ? 'text-gray-300 cursor-not-allowed' 
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
