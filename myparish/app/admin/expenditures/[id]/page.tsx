'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { expendituresApi } from '@/lib/api';
import { 
  ArrowLeft, Edit, Check, X, Trash2, DollarSign, 
  Calendar, FileText, User, Receipt, Clock, AlertTriangle 
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Expenditure {
  _id: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  receiptNumber?: string;
  vendor?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdBy: {
    _id: string;
    name: string;
    email: string;
  };
  approvedBy?: {
    _id: string;
    name: string;
    email: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export default function ExpenditureDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [expenditure, setExpenditure] = useState<Expenditure | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchExpenditure();
    }
  }, [params.id]);

  const fetchExpenditure = async () => {
    try {
      setIsLoading(true);
      const data = await expendituresApi.getById(params.id as string);
      setExpenditure(data);
    } catch (error) {
      console.error('Failed to fetch expenditure:', error);
      toast.error('Failed to load expenditure details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (status: 'approved' | 'rejected') => {
    if (!expenditure) return;

    try {
      await expendituresApi.updateStatus(expenditure._id, status);
      toast.success(`Expenditure ${status} successfully`);
      fetchExpenditure();
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update expenditure status');
    }
  };

  const handleDelete = async () => {
    if (!expenditure) return;
    if (!confirm('Are you sure you want to delete this expenditure?')) return;

    try {
      await expendituresApi.delete(expenditure._id);
      toast.success('Expenditure deleted successfully');
      router.push('/admin/expenditures');
    } catch (error) {
      console.error('Failed to delete expenditure:', error);
      toast.error('Failed to delete expenditure');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return Check;
      case 'rejected': return X;
      case 'pending': return Clock;
      default: return AlertTriangle;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading expenditure details...</span>
      </div>
    );
  }

  if (!expenditure) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Expenditure Not Found</h1>
          <p className="text-gray-600 mb-6">The expenditure you're looking for doesn't exist.</p>
          <Link
            href="/admin/expenditures"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Expenditures
          </Link>
        </div>
      </div>
    );
  }

  const StatusIcon = getStatusIcon(expenditure.status);

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link href="/admin/expenditures" className="mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-600 hover:text-gray-900" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Expenditure Details</h1>
            <p className="text-gray-600">View and manage expenditure information</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {expenditure.status === 'pending' && (
            <>
              <Link
                href={`/admin/expenditures/${expenditure._id}/edit`}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center"
              >
                <Edit size={16} className="mr-2" />
                Edit
              </Link>
              
              <button
                onClick={() => handleStatusUpdate('approved')}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center"
              >
                <Check size={16} className="mr-2" />
                Approve
              </button>
              
              <button
                onClick={() => handleStatusUpdate('rejected')}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center"
              >
                <X size={16} className="mr-2" />
                Reject
              </button>
              
              <button
                onClick={handleDelete}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center"
              >
                <Trash2 size={16} className="mr-2" />
                Delete
              </button>
            </>
          )}
        </div>
      </div>

      {/* Expenditure Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Expenditure Information</h2>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(expenditure.status)}`}>
                <StatusIcon size={14} className="mr-1" />
                {expenditure.status.charAt(0).toUpperCase() + expenditure.status.slice(1)}
              </span>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FileText size={16} className="inline mr-2" />
                    Description
                  </label>
                  <p className="text-gray-900 font-medium">{expenditure.description}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <DollarSign size={16} className="inline mr-2" />
                    Amount
                  </label>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(expenditure.amount)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <span className="px-3 py-1 text-sm font-medium bg-gray-100 text-gray-800 rounded-full">
                    {expenditure.category}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar size={16} className="inline mr-2" />
                    Date
                  </label>
                  <p className="text-gray-900">{formatDate(expenditure.date)}</p>
                </div>

                {expenditure.receiptNumber && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Receipt size={16} className="inline mr-2" />
                      Receipt Number
                    </label>
                    <p className="text-gray-900">{expenditure.receiptNumber}</p>
                  </div>
                )}

                {expenditure.vendor && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <User size={16} className="inline mr-2" />
                      Vendor/Supplier
                    </label>
                    <p className="text-gray-900">{expenditure.vendor}</p>
                  </div>
                )}
              </div>

              {expenditure.notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-900 whitespace-pre-wrap">{expenditure.notes}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Approval */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Status & Approval</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                <p className="text-gray-900">{expenditure.createdBy.name}</p>
                <p className="text-sm text-gray-500">{expenditure.createdBy.email}</p>
                <p className="text-sm text-gray-500">{formatDateTime(expenditure.createdAt)}</p>
              </div>

              {expenditure.approvedBy && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Approved By</label>
                  <p className="text-gray-900">{expenditure.approvedBy.name}</p>
                  <p className="text-sm text-gray-500">{expenditure.approvedBy.email}</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                <p className="text-sm text-gray-500">{formatDateTime(expenditure.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
