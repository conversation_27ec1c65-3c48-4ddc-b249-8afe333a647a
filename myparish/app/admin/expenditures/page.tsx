'use client';

import { useState, useEffect } from 'react';
import { expendituresApi } from '@/lib/api';
import { 
  Plus, Search, Filter, Download, Eye, Edit, Trash2, 
  Check, X, Clock, AlertTriangle, DollarSign, Calendar,
  TrendingUp, TrendingDown, Loader2
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface Expenditure {
  _id: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  receiptNumber?: string;
  vendor?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdBy: {
    _id: string;
    name: string;
    email: string;
  };
  approvedBy?: {
    _id: string;
    name: string;
    email: string;
  };
  notes?: string;
  createdAt: string;
}

interface BalanceSummary {
  year: number;
  income: {
    payments: number;
    offerings: number;
    total: number;
  };
  expenses: {
    total: number;
  };
  balance: number;
}

export default function ExpendituresPage() {
  const [expenditures, setExpenditures] = useState<Expenditure[]>([]);
  const [balanceSummary, setBalanceSummary] = useState<BalanceSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const categories = [
    'Maintenance', 'Utilities', 'Supplies', 'Events', 'Charity', 
    'Staff', 'Construction', 'Equipment', 'Transportation', 
    'Food', 'Medical', 'Education', 'Other'
  ];

  useEffect(() => {
    fetchExpenditures();
    fetchBalanceSummary();
  }, [selectedCategory, selectedStatus, selectedYear, currentPage]);

  const fetchExpenditures = async () => {
    try {
      setIsLoading(true);
      const params: any = {
        page: currentPage,
        limit: 20
      };

      if (selectedCategory) params.category = selectedCategory;
      if (selectedStatus) params.status = selectedStatus;
      if (selectedYear) params.year = selectedYear;

      const data = await expendituresApi.getAll(params);
      setExpenditures(data.expenditures);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Failed to fetch expenditures:', error);
      toast.error('Failed to load expenditures');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBalanceSummary = async () => {
    try {
      const data = await expendituresApi.getBalanceSummary(selectedYear);
      setBalanceSummary(data);
    } catch (error) {
      console.error('Failed to fetch balance summary:', error);
    }
  };

  const handleStatusUpdate = async (id: string, status: 'approved' | 'rejected') => {
    try {
      await expendituresApi.updateStatus(id, status);
      toast.success(`Expenditure ${status} successfully`);
      fetchExpenditures();
      fetchBalanceSummary();
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update expenditure status');
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this expenditure?')) return;

    try {
      await expendituresApi.delete(id);
      toast.success('Expenditure deleted successfully');
      fetchExpenditures();
      fetchBalanceSummary();
    } catch (error) {
      console.error('Failed to delete expenditure:', error);
      toast.error('Failed to delete expenditure');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return Check;
      case 'rejected': return X;
      case 'pending': return Clock;
      default: return AlertTriangle;
    }
  };

  const filteredExpenditures = expenditures.filter(expenditure =>
    expenditure.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expenditure.vendor?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expenditure.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading && expenditures.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2">Loading expenditures...</span>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Expenditures</h1>
          <p className="text-gray-600">Manage parish expenses and financial balance</p>
        </div>
        <Link
          href="/admin/expenditures/new"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus size={20} className="mr-2" />
          Add Expenditure
        </Link>
      </div>

      {/* Balance Summary Cards */}
      {balanceSummary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-green-50 p-6 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Total Income</p>
                <p className="text-2xl font-bold text-green-900">
                  {formatCurrency(balanceSummary.income.total)}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Payments: {formatCurrency(balanceSummary.income.payments)} | 
                  Offerings: {formatCurrency(balanceSummary.income.offerings)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-red-50 p-6 rounded-lg border border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-600 text-sm font-medium">Total Expenses</p>
                <p className="text-2xl font-bold text-red-900">
                  {formatCurrency(balanceSummary.expenses.total)}
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <div className={`p-6 rounded-lg border ${
            balanceSummary.balance >= 0 
              ? 'bg-blue-50 border-blue-200' 
              : 'bg-orange-50 border-orange-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${
                  balanceSummary.balance >= 0 ? 'text-blue-600' : 'text-orange-600'
                }`}>
                  Net Balance
                </p>
                <p className={`text-2xl font-bold ${
                  balanceSummary.balance >= 0 ? 'text-blue-900' : 'text-orange-900'
                }`}>
                  {formatCurrency(balanceSummary.balance)}
                </p>
              </div>
              <DollarSign className={`h-8 w-8 ${
                balanceSummary.balance >= 0 ? 'text-blue-600' : 'text-orange-600'
              }`} />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-medium">Year</p>
                <p className="text-2xl font-bold text-gray-900">{selectedYear}</p>
              </div>
              <Calendar className="h-8 w-8 text-gray-600" />
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search expenditures..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>

          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
            <Download size={20} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Expenditures Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created By
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredExpenditures.map((expenditure) => {
                const StatusIcon = getStatusIcon(expenditure.status);
                return (
                  <tr key={expenditure._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {expenditure.description}
                        </div>
                        {expenditure.vendor && (
                          <div className="text-sm text-gray-500">
                            Vendor: {expenditure.vendor}
                          </div>
                        )}
                        {expenditure.receiptNumber && (
                          <div className="text-sm text-gray-500">
                            Receipt: {expenditure.receiptNumber}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                        {expenditure.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(expenditure.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(expenditure.date)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(expenditure.status)}`}>
                        <StatusIcon size={12} className="mr-1" />
                        {expenditure.status.charAt(0).toUpperCase() + expenditure.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {expenditure.createdBy.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatDate(expenditure.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          href={`/admin/expenditures/${expenditure._id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Details"
                        >
                          <Eye size={16} />
                        </Link>

                        {expenditure.status === 'pending' && (
                          <>
                            <Link
                              href={`/admin/expenditures/${expenditure._id}/edit`}
                              className="text-gray-600 hover:text-gray-900"
                              title="Edit"
                            >
                              <Edit size={16} />
                            </Link>

                            <button
                              onClick={() => handleStatusUpdate(expenditure._id, 'approved')}
                              className="text-green-600 hover:text-green-900"
                              title="Approve"
                            >
                              <Check size={16} />
                            </button>

                            <button
                              onClick={() => handleStatusUpdate(expenditure._id, 'rejected')}
                              className="text-red-600 hover:text-red-900"
                              title="Reject"
                            >
                              <X size={16} />
                            </button>

                            <button
                              onClick={() => handleDelete(expenditure._id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete"
                            >
                              <Trash2 size={16} />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}

              {filteredExpenditures.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No expenditures found</h3>
                      <p className="text-gray-500">
                        {searchTerm || selectedCategory || selectedStatus
                          ? 'Try adjusting your filters'
                          : 'Get started by adding your first expenditure'
                        }
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{currentPage}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
