'use client';

import { useState, useEffect } from 'react';
import { paymentsApi } from '@/lib/api';
import { 
  DollarSign, Calendar, AlertTriangle, CheckCircle, 
  Clock, CreditCard, Eye, Plus, Loader2 
} from 'lucide-react';
import Link from 'next/link';

interface PaymentDue {
  _id: string;
  familyId: string;
  paymentTypeId: {
    _id: string;
    name: string;
  };
  year: number;
  period?: string;
  amount: number;
  isPaid: boolean;
  partialPayment: number;
  dueDate: string;
  createdAt: string;
}

interface FamilyDuesSummaryProps {
  familyId: string;
  showActions?: boolean;
  compact?: boolean;
  onRefresh?: () => void;
}

export default function FamilyDuesSummary({
  familyId,
  showActions = true,
  compact = false,
  onRefresh
}: FamilyDuesSummaryProps) {
  const [dues, setDues] = useState<PaymentDue[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDues();
  }, [familyId]);

  const fetchDues = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await paymentsApi.getPendingByFamily(familyId);
      setDues(data);
    } catch (error) {
      console.error('Failed to fetch family dues:', error);
      setError('Failed to load dues information');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRemainingAmount = (due: PaymentDue) => {
    return due.amount - due.partialPayment;
  };

  const getTotalOutstanding = () => {
    return dues.reduce((total, due) => {
      if (!due.isPaid) {
        return total + getRemainingAmount(due);
      }
      return total;
    }, 0);
  };

  const getStatusInfo = (due: PaymentDue) => {
    if (due.isPaid) {
      return {
        status: 'paid',
        label: 'Paid',
        color: 'text-green-600 bg-green-100',
        icon: CheckCircle
      };
    } else if (due.partialPayment > 0) {
      return {
        status: 'partial',
        label: 'Partial',
        color: 'text-yellow-600 bg-yellow-100',
        icon: Clock
      };
    } else if (new Date(due.dueDate) < new Date()) {
      return {
        status: 'overdue',
        label: 'Overdue',
        color: 'text-red-600 bg-red-100',
        icon: AlertTriangle
      };
    } else {
      return {
        status: 'pending',
        label: 'Pending',
        color: 'text-blue-600 bg-blue-100',
        icon: Clock
      };
    }
  };

  const getDueDescription = (due: PaymentDue) => {
    let description = due.paymentTypeId.name;
    if (due.year) {
      description += ` for ${due.year}`;
    }
    if (due.period) {
      description += ` (${due.period})`;
    }
    return description;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading dues...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (dues.length === 0) {
    return (
      <div className="p-6 text-center bg-gray-50 rounded-lg border border-gray-200">
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
        <p className="text-gray-600 font-medium">No outstanding dues</p>
        <p className="text-sm text-gray-500">This family has no pending payments</p>
      </div>
    );
  }

  const totalOutstanding = getTotalOutstanding();
  const pendingDues = dues.filter(due => !due.isPaid);
  const overdueDues = dues.filter(due => !due.isPaid && new Date(due.dueDate) < new Date());

  return (
    <div className="space-y-4">
      {/* Summary Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Dues Summary</h3>
            <p className="text-sm text-gray-600">
              {pendingDues.length} pending • {overdueDues.length} overdue
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">Total Outstanding</p>
            <p className="text-2xl font-bold text-blue-600">
              {formatCurrency(totalOutstanding)}
            </p>
          </div>
        </div>
      </div>

      {/* Dues List */}
      <div className="space-y-3">
        {dues.map((due) => {
          const statusInfo = getStatusInfo(due);
          const StatusIcon = statusInfo.icon;
          const remainingAmount = getRemainingAmount(due);

          return (
            <div 
              key={due._id}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-gray-900">
                      {getDueDescription(due)}
                    </h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusInfo.color}`}>
                      <StatusIcon size={12} className="inline mr-1" />
                      {statusInfo.label}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Total Amount</p>
                      <p className="font-medium">{formatCurrency(due.amount)}</p>
                    </div>
                    
                    {due.partialPayment > 0 && (
                      <div>
                        <p className="text-gray-500">Paid</p>
                        <p className="font-medium text-green-600">
                          {formatCurrency(due.partialPayment)}
                        </p>
                      </div>
                    )}
                    
                    {!due.isPaid && (
                      <div>
                        <p className="text-gray-500">Remaining</p>
                        <p className="font-medium text-red-600">
                          {formatCurrency(remainingAmount)}
                        </p>
                      </div>
                    )}
                    
                    <div>
                      <p className="text-gray-500">Due Date</p>
                      <p className="font-medium">{formatDate(due.dueDate)}</p>
                    </div>
                  </div>
                </div>

                {showActions && !due.isPaid && (
                  <div className="flex gap-2 ml-4">
                    <Link
                      href={`/admin/payments/dues/${due._id}`}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                      title="View Details"
                    >
                      <Eye size={16} />
                    </Link>
                    <Link
                      href={`/admin/payments/new?familyId=${familyId}&dueId=${due._id}`}
                      className="p-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                      title="Make Payment"
                    >
                      <CreditCard size={16} />
                    </Link>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {showActions && (
        <div className="pt-4 border-t border-gray-200">
          <Link
            href={`/admin/payments/new?familyId=${familyId}`}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Record New Payment
          </Link>
        </div>
      )}
    </div>
  );
}
