'use client';

import { ReactNode } from 'react';
import { useTheme } from '../context/ThemeContext';

interface DrawerProps {
  isOpen: boolean;
  children: ReactNode;
}

export default function Drawer({ isOpen, children }: DrawerProps) {
  const { colors } = useTheme();

  return (
    <div 
      className={`fixed inset-y-0 left-0 z-40 w-64 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } overflow-y-auto`}
      style={{ 
        backgroundColor: colors.card,
        color: colors.foreground,
        borderRight: `1px solid ${colors.border}`
      }}
    >
      <div className="p-4">
        {children}
      </div>
    </div>
  );
}