'use client';

import Link from "next/link";
import { useTheme } from "@/context/ThemeContext";
import { useState } from "react";
import { Menu, X, Home, Users, DollarSign, Settings } from "lucide-react";

export default function Navbar() {
  const { colors } = useTheme();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav style={{ backgroundColor: colors.primary, color: "white" }} className="shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="text-xl font-bold flex items-center space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            <span>Parish Management</span>
          </Link>
          
          {/* Desktop menu */}
          <div className="hidden md:flex space-x-6">
            <Link href="/" className="flex items-center space-x-1 hover:opacity-80 transition-opacity py-2 px-2">
              <Home size={18} />
              <span>Home</span>
            </Link>
            <Link href="/families" className="flex items-center space-x-1 hover:opacity-80 transition-opacity py-2 px-2">
              <Users size={18} />
              <span>Families</span>
            </Link>
            <Link href="/offerings" className="flex items-center space-x-1 hover:opacity-80 transition-opacity py-2 px-2">
              <DollarSign size={18} />
              <span>Offerings</span>
            </Link>
            <Link href="/admin" className="flex items-center space-x-1 hover:opacity-80 transition-opacity py-2 px-2">
              <Settings size={18} />
              <span>Admin</span>
            </Link>
          </div>
          
          {/* Mobile menu button */}
          <button 
            className="md:hidden p-2 rounded-md focus:outline-none"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
        
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-2 pb-6">
            <Link 
              href="/" 
              className="flex items-center space-x-2 py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10"
              onClick={() => setIsMenuOpen(false)}
            >
              <Home size={18} />
              <span>Home</span>
            </Link>
            <Link 
              href="/families" 
              className="flex items-center space-x-2 py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10"
              onClick={() => setIsMenuOpen(false)}
            >
              <Users size={18} />
              <span>Families</span>
            </Link>
            <Link 
              href="/offerings" 
              className="flex items-center space-x-2 py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10"
              onClick={() => setIsMenuOpen(false)}
            >
              <DollarSign size={18} />
              <span>Offerings</span>
            </Link>
            <Link 
              href="/admin" 
              className="flex items-center space-x-2 py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10"
              onClick={() => setIsMenuOpen(false)}
            >
              <Settings size={18} />
              <span>Admin</span>
            </Link>
          </div>
        )}
      </div>
    </nav>
  );
}
