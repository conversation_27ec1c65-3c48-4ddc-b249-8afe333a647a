'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTheme } from '../context/ThemeContext';
import {
  Home,
  Users,
  DollarSign,
  Calendar,
  User,
  Settings,
  LogOut,
  CreditCard,
  TrendingDown
} from 'lucide-react';
import { usePathname } from 'next/navigation';

export default function AdminSidebar() {
  const { colors } = useTheme();
  const pathname = usePathname();
  const router = useRouter();
  
  const menuItems = [
    { href: '/admin', label: 'Dashboard', icon: <Home size={20} /> },
    { href: '/admin/families', label: 'Families', icon: <Users size={20} /> },
    { href: '/admin/offerings', label: 'Offerings', icon: <DollarSign size={20} /> },
    { href: '/admin/payments', label: 'Payments', icon: <CreditCard size={20} /> },
    { href: '/admin/expenditures', label: 'Expenditures', icon: <TrendingDown size={20} /> },
    { href: '/admin/events', label: 'Events', icon: <Calendar size={20} /> },
    { href: '/admin/users', label: 'Users', icon: <User size={20} /> },
    { href: '/admin/settings', label: 'Settings', icon: <Settings size={20} /> },
  ];
  
  const isActive = (path: string) => {
    return pathname === path || pathname?.endsWith(`${path}/`);
  };
  
  const handleLogout = () => {
    // Clear authentication cookies
    document.cookie = 'isAuthenticated=; path=/; max-age=0';
    document.cookie = 'user=; path=/; max-age=0';
    
    // Redirect to login page
    router.push('/login');
  };
  
  return (
    <aside 
      className="fixed top-0 left-0 h-screen w-64 p-6 overflow-y-auto z-10 hidden md:block"
      style={{ backgroundColor: colors.card, borderRight: `1px solid ${colors.border}` }}
    >
      <h2 className="text-xl font-bold mb-6 flex items-center space-x-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="3" y1="9" x2="21" y2="9"></line>
          <line x1="9" y1="21" x2="9" y2="9"></line>
        </svg>
        <span>Admin Panel</span>
      </h2>
      <nav className="space-y-1">
        {menuItems.map((item) => (
          <Link 
            key={item.href} 
            href={item.href}
            className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
              isActive(item.href) 
                ? 'font-medium' 
                : 'hover:bg-opacity-10 hover:bg-gray-200'
            }`}
            style={{
              backgroundColor: isActive(item.href) 
                ? colors.primary 
                : 'transparent',
              color: isActive(item.href) 
                ? '#fff' 
                : colors.foreground,
            }}
          >
            {item.icon}
            <span>{item.label}</span>
          </Link>
        ))}
      </nav>
      
      <div className="absolute bottom-6 left-6 right-6">
        <button
          onClick={handleLogout}
          className="flex items-center space-x-3 p-3 rounded-lg w-full text-red-600 hover:bg-red-50 transition-colors"
          style={{ color: colors.error }}
        >
          <LogOut size={20} />
          <span>Logout</span>
        </button>
      </div>
    </aside>
  );
}
