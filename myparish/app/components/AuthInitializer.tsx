'use client';

import { useEffect, useRef } from 'react';
import { useAppDispatch } from '../redux/hooks';
import { restoreAuthState } from '../redux/slices/authSlice';

export default function AuthInitializer() {
  const dispatch = useAppDispatch();
  const initialized = useRef(false);
  
  // Restore auth state from localStorage on app initialization
  useEffect(() => {
    if (!initialized.current) {
      console.log('Initializing auth state');
      dispatch(restoreAuthState());
      initialized.current = true;
    }
  }, [dispatch]);
  
  // Add a listener for storage events to handle changes across tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' || e.key === 'user') {
        console.log('Auth storage changed, rehydrating state');
        dispatch(restoreAuthState());
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [dispatch]);
  
  return null; // This component doesn't render anything
}
