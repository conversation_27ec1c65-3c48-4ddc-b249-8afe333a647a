'use client';

import { useState } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { X, Moon, Sun, RotateCcw, Palette } from 'lucide-react';

export default function ThemeSidebar() {
  const [isOpen, setIsOpen] = useState(false);
  const { colors, updateColor, resetColors, isDarkMode, toggleDarkMode } = useTheme();

  const colorOptions = [
    { key: 'primary', label: 'Primary' },
    { key: 'secondary', label: 'Secondary' },
    { key: 'accent', label: 'Accent' },
    { key: 'background', label: 'Background' },
    { key: 'foreground', label: 'Text' },
    { key: 'card', label: 'Card' },
    { key: 'border', label: 'Border' },
    { key: 'success', label: 'Success' },
    { key: 'warning', label: 'Warning' },
    { key: 'error', label: 'Error' },
  ] as const;

  return (
    <>
      {/* Theme toggle button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-8 right-8 z-40 p-4 rounded-full shadow-lg flex items-center justify-center"
        style={{ backgroundColor: colors.primary, color: 'white' }}
      >
        <Palette size={24} />
      </button>

      {/* Theme sidebar */}
      <div 
        className={`fixed inset-y-0 right-0 z-50 w-96 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        } overflow-y-auto`}
        style={{ 
          backgroundColor: colors.card,
          color: colors.foreground,
          borderLeft: `1px solid ${colors.border}`
        }}
      >
        <div className="p-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Theme Settings</h2>
            <button 
              onClick={() => setIsOpen(false)}
              className="p-2 rounded-full hover:bg-opacity-10"
              style={{ backgroundColor: colors.foreground, color: colors.background, opacity: 0.2 }}
            >
              <X size={24} />
            </button>
          </div>

          <div className="mb-8 space-y-6">
            <div className="flex justify-between items-center">
              <span className="text-lg font-medium">Dark Mode</span>
              <button 
                onClick={toggleDarkMode}
                className="p-3 rounded-full"
                style={{ backgroundColor: isDarkMode ? colors.accent : colors.primary }}
              >
                {isDarkMode ? <Moon size={20} color="white" /> : <Sun size={20} color="white" />}
              </button>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-lg font-medium">Reset Colors</span>
              <button 
                onClick={resetColors}
                className="p-3 rounded-full"
                style={{ backgroundColor: colors.secondary, color: 'white' }}
              >
                <RotateCcw size={20} />
              </button>
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-xl font-medium mb-4">Color Palette</h3>
            
            {colorOptions.map(({ key, label }) => (
              <div key={key} className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <label className="text-lg font-medium">{label}</label>
                  <div 
                    className="w-8 h-8 rounded-full border-2"
                    style={{ 
                      backgroundColor: colors[key],
                      borderColor: colors.border
                    }}
                  />
                </div>
                <input
                  type="color"
                  value={colors[key]}
                  onChange={(e) => updateColor(key, e.target.value)}
                  className="w-full h-8 rounded cursor-pointer"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
