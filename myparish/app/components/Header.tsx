'use client';

import { useState } from 'react';
import { Menu, Bell, User, Search, ChevronDown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTheme } from '../context/ThemeContext';
import { useAppSelector } from '../redux/hooks';

interface HeaderProps {
  toggleSidebar?: () => void;
}

export default function Header({ toggleSidebar }: HeaderProps) {
  const router = useRouter();
  const { colors } = useTheme();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const { user } = useAppSelector(state => state.auth);
  
  const handleLogout = () => {
    // Clear authentication cookies
    document.cookie = 'isAuthenticated=; path=/; max-age=0';
    document.cookie = 'user=; path=/; max-age=0';
    
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Redirect to login page
    router.push('/login');
  };
  
  return (
    <header 
      className="h-16 flex items-center justify-between px-6 border-b"
      style={{ backgroundColor: colors.card, borderColor: colors.border }}
    >
      <div className="flex items-center">
        {toggleSidebar && (
          <button 
            onClick={toggleSidebar}
            className="p-2 rounded-lg mr-4 md:hidden"
            style={{ color: colors.foreground }}
          >
            <Menu size={20} />
          </button>
        )}
        
        <div className="relative">
          <input
            type="text"
            placeholder="Search..."
            className="w-64 py-2 pl-10 pr-4 rounded-lg text-sm focus:outline-none"
            style={{ 
              backgroundColor: `${colors.foreground}30`,
              color: colors.foreground,
              borderColor: colors.border
            }}
          />
          <Search size={16} className="absolute left-3 top-2.5 opacity-50" />
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <button 
          className="p-2 rounded-full relative"
          style={{ backgroundColor: `${colors.foreground}30` }}
        >
          <Bell size={18} />
          <span 
            className="absolute top-0 right-0 w-2 h-2 rounded-full"
            style={{ backgroundColor: colors.primary }}
          ></span>
        </button>
        
        <div className="relative">
          <button 
            onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
            className="flex items-center space-x-2 p-2 rounded-lg"
            style={{ backgroundColor: `${colors.foreground}30` }}
          >
            <div 
              className="w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: colors.primary }}
            >
              <User size={16} className="text-white" />
            </div>
            <div className="hidden md:block text-left">
              <p className="text-sm font-medium">{user?.name || 'Admin User'}</p>
              <p className="text-xs opacity-70">{user?.role || 'Administrator'}</p>
            </div>
            <ChevronDown size={16} className="hidden md:block opacity-70" />
          </button>
          
          {isProfileMenuOpen && (
            <div 
              className="absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 z-10"
              style={{ backgroundColor: colors.card, borderColor: colors.border }}
            >
              <a 
                href="/admin/profile" 
                className="block px-4 py-2 text-sm hover:bg-opacity-10 hover:bg-gray-200"
                style={{ color: colors.foreground, backgroundColor: 'transparent' }}
              >
                Your Profile
              </a>
              <a 
                href="/admin/settings" 
                className="block px-4 py-2 text-sm hover:bg-opacity-10 hover:bg-gray-200"
                style={{ color: colors.foreground, backgroundColor: 'transparent' }}
              >
                Settings
              </a>
              <button 
                onClick={handleLogout}
                className="block w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 hover:bg-red-50"
                style={{ color: colors.error }}
              >
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
