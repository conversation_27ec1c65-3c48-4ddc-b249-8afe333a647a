export interface Event {
  _id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  location: string;
  type: string;
  isAllDay: boolean;
  organizer: string;
  status: string;
  maxAttendees?: string;
  isPublic?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Family {
  _id: string;
  name: string;
  address: string;
  phone: string;
  email?: string;
  members: FamilyMember[];
  photo?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface FamilyMember {
  _id?: string;
  name: string;
  relationship: string;
  dateOfBirth?: string;
  phone?: string;
  email?: string;
}

export interface Event {
  _id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  location: string;
  type: string;
  isAllDay: boolean;
  organizer: string;
  status: string;
  maxAttendees?: string;
  isPublic?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Offering {
  _id: string;
  amount: number;
  date: string;
  type: string;
  family?: string | Family;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Settings {
  _id: string;
  parishName: string;
  parishAddress: string;
  parishPhone: string;
  parishEmail: string;
  parishWebsite?: string;
  logo?: string;
  createdAt?: string;
  updatedAt?: string;
}