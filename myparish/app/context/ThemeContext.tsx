'use client';

import { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppSelector, useAppDispatch } from '../redux/hooks';
import { selectTheme, updateColor, resetColors, toggleDarkMode } from '../redux/slices/themeSlice';
import type { RootState } from '../redux/store';

type ThemeColors = {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  card: string;
  border: string;
  success: string;
  warning: string;
  error: string;
};

type ThemeContextType = {
  colors: ThemeColors;
  updateColor: (key: keyof ThemeColors, value: string) => void;
  resetColors: () => void;
  isDarkMode: boolean;
  toggleDarkMode: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: ReactNode }) {
  const { colors, isDarkMode } = useAppSelector(selectTheme);
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Update CSS variables
    Object.entries(colors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--color-${key}`, value as string);
    });
  }, [colors]);
  
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const handleUpdateColor = (key: keyof ThemeColors, value: string) => {
    dispatch(updateColor({ key, value }));
  };

  const handleResetColors = () => {
    dispatch(resetColors());
  };
  
  const handleToggleDarkMode = () => {
    dispatch(toggleDarkMode());
  };

  return (
    <ThemeContext.Provider value={{ 
      colors, 
      updateColor: handleUpdateColor, 
      resetColors: handleResetColors, 
      isDarkMode, 
      toggleDarkMode: handleToggleDarkMode 
    }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
