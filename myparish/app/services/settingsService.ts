import apiClient from './apiClient';
import { Settings } from '../types';

export const fetchSettings = async (): Promise<Settings> => {
  const response = await apiClient.get('/settings');
  return response.data;
};

export const updateSettings = async (settingsData: Partial<Settings>): Promise<Settings> => {
  const response = await apiClient.put('/settings', settingsData);
  return response.data;
};