import apiClient from './apiClient';
import { Offering } from '../types';

export const fetchOfferings = async (params = {}): Promise<Offering[]> => {
  const response = await apiClient.get('/offerings', { params });
  return response.data;
};

export const fetchOffering = async (id: string): Promise<Offering> => {
  const response = await apiClient.get(`/offerings/${id}`);
  return response.data;
};

export const createOffering = async (offeringData: Partial<Offering>): Promise<Offering> => {
  const response = await apiClient.post('/offerings', offeringData);
  return response.data;
};

export const updateOffering = async (id: string, offeringData: Partial<Offering>): Promise<Offering> => {
  const response = await apiClient.put(`/offerings/${id}`, offeringData);
  return response.data;
};

export const deleteOffering = async (id: string): Promise<void> => {
  await apiClient.delete(`/offerings/${id}`);
};