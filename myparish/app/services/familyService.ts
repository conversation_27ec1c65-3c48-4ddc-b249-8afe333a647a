import apiClient from './apiClient';
import { Family } from '../types';

export const fetchFamilies = async (): Promise<Family[]> => {
  const response = await apiClient.get('/families');
  return response.data;
};

export const fetchFamily = async (id: string): Promise<Family> => {
  const response = await apiClient.get(`/families/${id}`);
  return response.data;
};

export const createFamily = async (formData: FormData): Promise<Family> => {
  const response = await apiClient.post('/families', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const updateFamily = async (id: string, formData: FormData): Promise<Family> => {
  const response = await apiClient.put(`/families/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const deleteFamily = async (id: string): Promise<void> => {
  await apiClient.delete(`/families/${id}`);
};