@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #111827;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Keeping light colors even for dark mode preference */
    --background: #ffffff;
    --foreground: #111827;
  }
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
}

.input-group {
  @apply mb-1;
}

.input-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.input-field {
  @apply w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200;
}

.file-input {
  @apply w-full p-2 border border-gray-300 rounded-md text-sm file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100 focus:outline-none;
}

/* Add these animations to your existing globals.css */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes counterAnimation {
  from { opacity: 0.5; }
  to { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite ease-in-out;
}

.animate-counter {
  animation: counterAnimation 0.5s ease-out;
}

/* Card hover effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient backgrounds for cards */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), rgba(59, 130, 246, 0.8));
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary), rgba(124, 58, 237, 0.8));
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--color-accent), rgba(219, 39, 119, 0.8));
}
