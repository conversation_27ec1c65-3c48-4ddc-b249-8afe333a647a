{"name": "myparish", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "framer-motion": "^12.9.6", "lucide-react": "^0.507.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-to-print": "^3.1.0", "react-toastify": "^11.0.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sweetalert2": "^11.21.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}