'use client';

import { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';

interface ImageWithFallbackProps extends Omit<ImageProps, 'src' | 'onError'> {
  src: string | null | undefined;
  fallbackSrc?: string;
}

export default function ImageWithFallback({
  src,
  fallbackSrc = '/placeholder-image.jpg',
  alt,
  className,
  ...rest
}: ImageWithFallbackProps) {
  // Process the source URL
  const processImageSrc = (source: string | null | undefined): string => {
    if (!source) return fallbackSrc;
    
    // If already a full URL or fallback, return as is
    if (source.startsWith('http') || source === fallbackSrc) {
      return source;
    }
    
    // Make sure src starts with a slash
    const normalizedSrc = source.startsWith('/') ? source : `/${source}`;
    
    // Explicitly use localhost:4001
    return `http://localhost:4001${normalizedSrc}`;
  };
  
  // Initialize with processed src
  const [imgSrc, setImgSrc] = useState<string>(processImageSrc(src));
  const [error, setError] = useState<boolean>(false);
  
  // Update imgSrc if src prop changes
  useEffect(() => {
    setImgSrc(processImageSrc(src));
    setError(false); // Reset error state when src changes
  }, [src]);

  console.log('Using image source:', imgSrc);

  return (
    <div className={`relative w-full h-full ${error ? 'bg-gray-100' : ''}`}>
      <Image
        {...rest}
        src={imgSrc}
        alt={alt || 'Image'}
        className={`object-cover ${className || ''}`}
        onError={() => {
          console.error('Image failed to load:', imgSrc);
          setError(true);
          setImgSrc(fallbackSrc);
        }}
        unoptimized={true}
        fill={rest.fill === undefined ? true : rest.fill}
        sizes={rest.sizes || "(max-width: 768px) 100vw, 50vw"}
      />
    </div>
  );
}
