import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;
  
  // Check if the path is for admin routes or root
  const isAdminRoute = path.startsWith('/admin');
  const isRootPath = path === '/';
  
  // Check if the user is authenticated from cookies
  const isAuthenticated = request.cookies.get('isAuthenticated')?.value === 'true';
  
  // If trying to access admin routes without authentication
  if (isAdminRoute && !isAuthenticated) {
    // Redirect to the login page
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // If accessing root path, redirect based on authentication
  if (isRootPath) {
    if (isAuthenticated) {
      return NextResponse.redirect(new URL('/admin', request.url));
    } else {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }
  
  // If already authenticated and trying to access login
  if (path === '/login' && isAuthenticated) {
    // Redirect to admin dashboard
    return NextResponse.redirect(new URL('/admin', request.url));
  }
  
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: ['/', '/admin/:path*', '/login'],
};
