import mongoose, { Document, Schema } from 'mongoose';

export interface ISettings extends Document {
  parishName: string;
  parishAddress: string;
  parishPhone: string;
  parishEmail: string;
  parishWebsite?: string;
  logo?: string;
  banner?: string;
  church?: string;
  gallery?: string[];
  emailNotifications: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  updatedAt: Date;
}

const SettingsSchema = new Schema<ISettings>({
  parishName: { type: String, required: true },
  parishAddress: { type: String, required: true },
  parishPhone: { type: String, required: true },
  parishEmail: { type: String, required: true },
  parishWebsite: { type: String },
  logo: { type: String },
  banner: { type: String },
  church: { type: String },
  gallery: [{ type: String }],
  emailNotifications: { type: Boolean, default: true },
  backupFrequency: { type: String, enum: ['daily', 'weekly', 'monthly'], default: 'daily' },
  theme: {
    primary: { type: String, default: '#4f46e5' },
    secondary: { type: String, default: '#8b5cf6' },
    accent: { type: String, default: '#ec4899' }
  }
}, { timestamps: true });

export default mongoose.model<ISettings>('Settings', SettingsSchema);