import mongoose, { Document, Schema } from 'mongoose';

interface IMember {
  name: string;
  relationship: string;
  dob: Date;
  baptism?: Date;
  hc?: Date;
  uruthipoosal?: Date;
  marriage?: Date;
  education: string;
  occupation: string;
  photo?: string;
}

export interface IFamily extends Document {
  slNo: string;
  headName: string;
  gender: string;
  uraviam: string;
  houseNo: string;
  address: string;
  wardP: string;
  wardC: string;
  occupation: string;
  annualIncome: string;
  houseType: string;
  infrastructure: string;
  headDOB: Date;
  headBaptism?: Date;
  headHC?: Date;
  headUruthipoosal?: Date;
  headMarriage?: Date;
  headEducation: string;
  spouseName?: string;
  spouseDOB?: Date;
  spouseBaptism?: Date;
  spouseHC?: Date;
  spouseUruthipoosal?: Date;
  spouseOccupation?: string;
  spouseEducation?: string;
  members: IMember[];
  headPhoto?: string;
  spousePhoto?: string;
  phone: string;
  email?: string;
  createdAt: Date;
  updatedAt: Date;
}

const MemberSchema = new Schema<IMember>({
  name: { type: String, required: true },
  relationship: { type: String, required: true },
  dob: { type: Date, required: true },
  baptism: { type: Date },
  hc: { type: Date },
  uruthipoosal: { type: Date },
  marriage: { type: Date },
  education: { type: String },
  occupation: { type: String },
  photo: { type: String }
});

const FamilySchema = new Schema<IFamily>({
  slNo: { type: String, required: true, unique: true },
  headName: { type: String, required: true },
  gender: { type: String, required: true },
  uraviam: { type: String, required: true },
  houseNo: { type: String, required: true },
  address: { type: String, required: true },
  wardP: { type: String, required: true },
  wardC: { type: String, required: true },
  occupation: { type: String, required: true },
  annualIncome: { type: String, required: true },
  houseType: { type: String, required: true },
  infrastructure: { type: String, required: true },
  headDOB: { type: Date, required: true },
  headBaptism: { type: Date },
  headHC: { type: Date },
  headUruthipoosal: { type: Date },
  headMarriage: { type: Date },
  headEducation: { type: String, required: true },
  spouseName: { type: String },
  spouseDOB: { type: Date },
  spouseBaptism: { type: Date },
  spouseHC: { type: Date },
  spouseUruthipoosal: { type: Date },
  spouseOccupation: { type: String },
  spouseEducation: { type: String },
  members: [MemberSchema],
  headPhoto: { type: String },
  spousePhoto: { type: String },
  phone: { type: String, required: true },
  email: { type: String }
}, { timestamps: true });

export default mongoose.model<IFamily>('Family', FamilySchema);