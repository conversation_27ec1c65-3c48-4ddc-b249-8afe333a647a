import mongoose, { Document, Schema } from 'mongoose';

export interface IOffering extends Document {
  date: Date;
  familyId: mongoose.Types.ObjectId;
  type: string;
  amount: number;
  purpose: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const OfferingSchema = new Schema<IOffering>({
  date: { type: Date, required: true },
  familyId: { type: Schema.Types.ObjectId, ref: 'Family', required: true },
  type: { type: String, required: true },
  amount: { type: Number, required: true },
  purpose: { type: String, required: true },
  notes: { type: String }
}, { timestamps: true });

export default mongoose.model<IOffering>('Offering', OfferingSchema);