import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentType extends Document {
  name: string;
  description: string;
  amount: number;
  frequency: 'yearly' | 'monthly' | 'quarterly' | 'one-time';
  isActive: boolean;
  // Date range fields for multi-period payment types
  startYear?: number;
  endYear?: number;
  startMonth?: number; // 1-12
  endMonth?: number; // 1-12
  createdAt: Date;
  updatedAt: Date;
}

const PaymentTypeSchema = new Schema<IPaymentType>({
  name: { type: String, required: true, unique: true },
  description: { type: String },
  amount: { type: Number, required: true },
  frequency: {
    type: String,
    enum: ['yearly', 'monthly', 'quarterly', 'one-time'],
    default: 'yearly'
  },
  isActive: { type: Boolean, default: true },
  // Date range fields for multi-period payment types
  startYear: {
    type: Number,
    min: 2000,
    max: 2100,
    validate: {
      validator: function(this: IPaymentType, value: number) {
        // Required for yearly and monthly frequencies
        return this.frequency === 'one-time' || this.frequency === 'quarterly' || value != null;
      },
      message: 'Start year is required for yearly and monthly payment types'
    }
  },
  endYear: {
    type: Number,
    min: 2000,
    max: 2100,
    validate: {
      validator: function(this: IPaymentType, value: number) {
        // Required for yearly and monthly frequencies
        if (this.frequency === 'one-time' || this.frequency === 'quarterly') return true;
        if (value == null) return false;
        return this.startYear ? value >= this.startYear : true;
      },
      message: 'End year is required and must be >= start year for yearly and monthly payment types'
    }
  },
  startMonth: {
    type: Number,
    min: 1,
    max: 12,
    validate: {
      validator: function(this: IPaymentType, value: number) {
        // Required only for monthly frequency
        return this.frequency !== 'monthly' || value != null;
      },
      message: 'Start month is required for monthly payment types'
    }
  },
  endMonth: {
    type: Number,
    min: 1,
    max: 12,
    validate: {
      validator: function(this: IPaymentType, value: number) {
        // Required only for monthly frequency
        if (this.frequency !== 'monthly') return true;
        if (value == null) return false;
        // If same year, end month should be >= start month
        if (this.startYear === this.endYear && this.startMonth) {
          return value >= this.startMonth;
        }
        return true;
      },
      message: 'End month is required for monthly payment types and must be valid'
    }
  }
}, { timestamps: true });

export default mongoose.model<IPaymentType>('PaymentType', PaymentTypeSchema);