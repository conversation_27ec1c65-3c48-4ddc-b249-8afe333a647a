import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface I<PERSON>ser extends Document {
  name: string;
  email: string;
  password: string;
  role: 'Admin' | 'User';
  status: 'Active' | 'Inactive';
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema: Schema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['Admin', 'User'], default: 'User' },
  status: { type: String, enum: ['Active', 'Inactive'], default: 'Active' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Hash password before saving
UserSchema.pre<IUser>('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Generate salt
    const salt = await bcrypt.genSalt(10);
    // Hash password
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    // First check if the password is already hashed (starts with $2a$, $2b$, or $2y$)
    if (this.password.startsWith('$2')) {
      // Use bcrypt to compare
      return await bcrypt.compare(candidatePassword, this.password);
    } else {
      // Direct comparison for plain text passwords
      return this.password === candidatePassword;
    }
  } catch (error) {
    throw error;
  }
};

export default mongoose.model<IUser>('User', UserSchema);
