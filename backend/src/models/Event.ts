import mongoose, { Document, Schema } from 'mongoose';

export interface IEvent extends Document {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location: string;
  type: string;
  isAllDay: boolean;
  attendees?: mongoose.Types.ObjectId[];
  organizer?: string;
  status: 'Scheduled' | 'Cancelled' | 'Completed';
  createdAt: Date;
  updatedAt: Date;
}

const EventSchema = new Schema<IEvent>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  location: { type: String, required: true },
  type: { type: String, required: true },
  isAllDay: { type: Boolean, default: false },
  attendees: [{ type: Schema.Types.ObjectId, ref: 'Family' }],
  organizer: { type: String },
  status: { type: String, enum: ['Scheduled', 'Cancelled', 'Completed'], default: 'Scheduled' }
}, { timestamps: true });

export default mongoose.model<IEvent>('Event', EventSchema);