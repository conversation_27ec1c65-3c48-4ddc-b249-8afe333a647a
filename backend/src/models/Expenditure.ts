import mongoose, { Document, Schema } from 'mongoose';

export interface IExpenditure extends Document {
  description: string;
  amount: number;
  category: string;
  date: Date;
  receiptNumber?: string;
  vendor?: string;
  approvedBy: mongoose.Types.ObjectId;
  notes?: string;
  attachments?: string[];
  status: 'pending' | 'approved' | 'rejected';
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const ExpenditureSchema = new Schema<IExpenditure>(
  {
    description: {
      type: String,
      required: true,
      trim: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    category: {
      type: String,
      required: true,
      enum: [
        'Maintenance',
        'Utilities',
        'Supplies',
        'Events',
        'Charity',
        'Staff',
        'Construction',
        'Equipment',
        'Transportation',
        'Food',
        'Medical',
        'Education',
        'Other'
      ]
    },
    date: {
      type: Date,
      required: true,
      default: Date.now
    },
    receiptNumber: {
      type: String,
      trim: true
    },
    vendor: {
      type: String,
      trim: true
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: function(this: IExpenditure) {
        return this.status === 'approved';
      }
    },
    notes: {
      type: String,
      trim: true
    },
    attachments: [{
      type: String // File paths or URLs
    }],
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  {
    timestamps: true
  }
);

// Index for better query performance
ExpenditureSchema.index({ date: -1 });
ExpenditureSchema.index({ category: 1 });
ExpenditureSchema.index({ status: 1 });
ExpenditureSchema.index({ createdBy: 1 });

export default mongoose.model<IExpenditure>('Expenditure', ExpenditureSchema);
