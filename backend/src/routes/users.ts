import express, { Router } from 'express';
import User from '../models/User.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router : Router = express.Router();

// Get all users
router.get('/', authenticate, async (req, res) => {
  try {
    const users = await User.find().select('-password');
  
    
    res.json(users);
  } catch (error: any) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get user by ID (admin only)
router.get(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id).select('-password');
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      res.json(user);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Create new user (admin only)
router.post(
  '/',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { email } = req.body;
      
      // Check if user already exists
      const existingUser = await User.findOne({ email });
      
      if (existingUser) {
        return res.status(400).json({ message: 'User already exists' });
      }
      
      const user = new User(req.body);
      await user.save();
      
      // Don't return password
      const userResponse = user.toObject();
      delete (userResponse as { password?: string }).password;
      
      res.status(201).json(userResponse);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update user (admin only)
router.put(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      // Don't allow password updates through this endpoint
      const userData = { ...req.body };
      delete userData.password;
      
      const user = await User.findByIdAndUpdate(
        req.params.id,
        userData,
        { new: true }
      ).select('-password');
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      res.json(user);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete user (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      // Prevent deleting the last admin
      const currentUser = req.user as { _id: string };
      if (req.params.id === currentUser?._id.toString()) {
        return res.status(400).json({ message: 'Cannot delete your own account' });
      }
      
      const adminCount = await User.countDocuments({ role: 'Admin' });
      const userToDelete = await User.findById(req.params.id);
      
      if (userToDelete?.role === 'Admin' && adminCount <= 1) {
        return res.status(400).json({ message: 'Cannot delete the last admin account' });
      }
      
      const deletedUser = await User.findByIdAndDelete(req.params.id);
      
      if (!currentUser) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      res.json({ message: 'User deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Reset user password (admin only)
router.post(
  '/:id/reset-password',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { newPassword } = req.body;
      
      const user = await User.findById(req.params.id);
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      user.password = newPassword;
      await user.save();
      
      res.json({ message: 'Password reset successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;
