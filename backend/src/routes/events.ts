import express, { Router } from 'express';
import Event from '../models/Event.ts';
import { authenticate, authorize } from '../middleware/auth.ts';
import { log } from 'console';

const router: Router = express.Router();

// Get all events
router.get('/', authenticate, async (req, res) => {
  try {
    const { startDate, endDate, status } = req.query;
    
    const query: any = {};
    
    if (startDate && endDate) {
      query.startDate = { $gte: new Date(startDate as string) };
      query.endDate = { $lte: new Date(endDate as string) };
    }
    
    if (status) {
      query.status = status;
    }
    
    const events = await Event.find(query)
      .populate('attendees', 'headName slNo')
      .sort({ startDate: 1 });
    
    res.json(events);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get event by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate('attendees', 'headName slNo');
    
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }
    
    res.json(event);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new event
router.post(
  '/',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const event = new Event(req.body);
      await event.save();
      
      res.status(201).json(event);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update event
router.put(
  '/:id',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const event = await Event.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true }
      );
      
      if (!event) {
        return res.status(404).json({ message: 'Event not found' });
      }
      
      res.json(event);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete event
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const event = await Event.findByIdAndDelete(req.params.id);
      
      if (!event) {
        return res.status(404).json({ message: 'Event not found' });
      }
      
      res.json({ message: 'Event deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Add attendee to event
router.post(
  '/:id/attendees',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const { familyId } = req.body;
      
      const event = await Event.findById(req.params.id);
      
      if (!event) {
        return res.status(404).json({ message: 'Event not found' });
      }
      
      if (!event.attendees) {
        event.attendees = [];
      }
      
      // Check if family is already an attendee
      if (event.attendees.includes(familyId)) {
        return res.status(400).json({ message: 'Family is already an attendee' });
      }
      
      event.attendees.push(familyId);
      await event.save();
      
      res.json(event);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Remove attendee from event
router.delete(
  '/:id/attendees/:familyId',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);
      
      if (!event) {
        return res.status(404).json({ message: 'Event not found' });
      }
      
      if (!event.attendees) {
        return res.status(400).json({ message: 'No attendees for this event' });
      }
      
      // Filter out the family ID
      event.attendees = event.attendees.filter(
        attendee => attendee.toString() !== req.params.familyId
      );
      
      await event.save();
      
      res.json(event);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Get upcoming events
router.get('/upcoming/list', authenticate, async (req, res) => {
  try {
    const now = new Date();
    
    const events = await Event.find({
      startDate: { $gte: now },
      status: 'Scheduled'
    })
      .sort({ startDate: 1 })
      .limit(5);
    
    res.json(events);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

export default router;
