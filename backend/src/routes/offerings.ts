import express, { Router } from 'express';
import Offering from '../models/Offering.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router: Router = express.Router();

// Get all offerings
router.get('/', authenticate, async (req, res) => {
  try {
    const offerings = await Offering.find()
      .populate('familyId', 'headName slNo')
      .sort({ date: -1 });
    
    res.json(offerings);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get offering by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const offering = await Offering.findById(req.params.id)
      .populate('familyId', 'headName slNo');
    
    if (!offering) {
      return res.status(404).json({ message: 'Offering not found' });
    }
    
    res.json(offering);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new offering
router.post(
  '/',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const offering = new Offering(req.body);
      await offering.save();
      
      res.status(201).json(offering);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update offering
router.put(
  '/:id',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const offering = await Offering.findByIdAndUpdate(
        req.params.id,
        req.body,
        { new: true }
      );
      
      if (!offering) {
        return res.status(404).json({ message: 'Offering not found' });
      }
      
      res.json(offering);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete offering
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const offering = await Offering.findByIdAndDelete(req.params.id);
      
      if (!offering) {
        return res.status(404).json({ message: 'Offering not found' });
      }
      
      res.json({ message: 'Offering deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Get offerings by family
router.get('/family/:familyId', authenticate, async (req, res) => {
  try {
    const offerings = await Offering.find({ familyId: req.params.familyId })
      .sort({ date: -1 });
    
    res.json(offerings);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get offerings summary
router.get('/summary/stats', authenticate, async (req, res) => {
  try {
    const { startDate, endDate, type } = req.query;
    
    const query: any = {};
    
    if (startDate && endDate) {
      query.date = {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string)
      };
    }
    
    if (type) {
      query.type = type;
    }
    
    const totalAmount = await Offering.aggregate([
      { $match: query },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    
    const countByType = await Offering.aggregate([
      { $match: query },
      { $group: { _id: '$type', count: { $sum: 1 }, total: { $sum: '$amount' } } }
    ]);
    
    const monthlyTotals = await Offering.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          total: { $sum: '$amount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);
    
    res.json({
      totalAmount: totalAmount.length > 0 ? totalAmount[0].total : 0,
      countByType,
      monthlyTotals
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

export default router;