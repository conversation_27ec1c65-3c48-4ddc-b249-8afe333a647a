import express, { Router } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User.ts';
import { authenticate } from '../middleware/auth.ts';
import mongoose from 'mongoose';

const router: Router = express.Router();

// Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user by email
    const user = await User.findOne({ email });
    
    if (!user || user.status !== 'Active') {
      return res.status(401).json({ message: 'Invalid credentials or inactive account' });
    }
    
    let isMatch = false;
    
    // Check if the user document has a comparePassword method (for hashed passwords)
    if (typeof user.comparePassword === 'function') {
      // Check password using the model's comparePassword method
      isMatch = await user.comparePassword(password);
    } else {
      // Direct comparison for plain text passwords (for admin users created via secret endpoint)
      isMatch = (user.password === password);
    }
    
    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { id: user._id },
      process.env.JWT_SECRET || 'default_secret',
      { expiresIn: '7d' }
    );
    
    // Return user info and token
    res.json({
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get current user
router.get('/me', authenticate, async (req, res) => {
  try {
    res.json({
      user: {
        id: req.user?._id,
        name: req.user?.name,
        email: req.user?.email,
        role: req.user?.role
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Change password
router.post('/change-password', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    // Check current password
    const isMatch = await req.user.comparePassword(currentPassword);
    
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }
    
    // Update password
    req.user.password = newPassword;
    await req.user.save();
    
    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Add a secret endpoint to create admin user
router.post('/create-admin', async (req, res) => {
  try {
    // Check for secret key in headers
    const secretKey = req.headers['x-admin-secret'];
    const configSecret = process.env.ADMIN_SECRET_KEY || 'default_admin_secret';
    
    if (secretKey !== configSecret) {
      return res.status(401).json({ message: 'Unauthorized: Invalid secret key' });
    }
    
    const { name, email, password } = req.body;
    
    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({ message: 'All fields are required' });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Create user with Admin role, bypassing password hashing
    if (!mongoose.connection.db) {
      return res.status(500).json({ message: 'Database connection is not established' });
    }

    await mongoose.connection.db.collection('users').insertOne({
      name,
      email,
      password, // Plain text password (not hashed)
      role: 'Admin',
      status: 'Active',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    res.status(201).json({ message: 'Admin user created successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

export default router;
