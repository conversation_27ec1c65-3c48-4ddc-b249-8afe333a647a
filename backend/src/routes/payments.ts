import express, { Router } from 'express';
import mongoose from 'mongoose';
import Payment from '../models/Payment.ts';
import PaymentDue from '../models/PaymentDue.ts';
import Family from '../models/Family.ts';
import PaymentType from '../models/PaymentType.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router: Router = express.Router();

// Get all payment dues
router.get('/dues', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    
    const query: any = {};
    
    // Filter by family
    if (req.query.familyId) {
      query.familyId = req.query.familyId;
    }
    
    // Filter by payment type
    if (req.query.paymentTypeId) {
      query.paymentTypeId = req.query.paymentTypeId;
    }
    
    // Filter by year
    if (req.query.year) {
      query.year = parseInt(req.query.year as string);
    }
    
    // Filter by payment status - default to unpaid only
    const status = req.query.status as string;
    if (status === 'paid') {
      query.isPaid = true;
    } else if (status === 'unpaid') {
      query.isPaid = false;
    } else if (status === 'partial') {
      query.isPaid = false;
      query.partialPayment = { $gt: 0 };
    } else if (status === 'all') {
      // Show all dues regardless of payment status
    } else {
      // Default: show only unpaid dues
      query.isPaid = false;
    }
    
    // Filter by due date range
    if (req.query.startDate && req.query.endDate) {
      query.dueDate = {
        $gte: new Date(req.query.startDate as string),
        $lte: new Date(req.query.endDate as string)
      };
    }
    
    // Handle search
    if (req.query.search) {
      // First get matching families
      const searchRegex = new RegExp(req.query.search as string, 'i');
      const matchingFamilies = await Family.find({
        $or: [
          { headName: searchRegex },
          { slNo: searchRegex }
        ]
      }).select('_id');
      
      const familyIds = matchingFamilies.map(f => f._id);
      
      // Also get matching payment types
      const matchingTypes = await PaymentType.find({
        name: searchRegex
      }).select('_id');
      
      const typeIds = matchingTypes.map(t => t._id);
      
      // Add to query
      if (familyIds.length > 0 || typeIds.length > 0) {
        query.$or = [];
        
        if (familyIds.length > 0) {
          query.$or.push({ familyId: { $in: familyIds } });
        }
        
        if (typeIds.length > 0) {
          query.$or.push({ paymentTypeId: { $in: typeIds } });
        }
      } else {
        // No matches found, return empty result
        return res.json({
          dues: [],
          pagination: {
            total: 0,
            page,
            pages: 0
          }
        });
      }
    }
    
    const total = await PaymentDue.countDocuments(query);
    
    const dues = await PaymentDue.find(query)
      .populate('familyId', 'headName slNo')
      .populate('paymentTypeId', 'name')
      .sort({ dueDate: 1 })
      .skip(skip)
      .limit(limit);
    
    res.json({
      dues,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment due by ID
router.get('/dues/:id', authenticate, async (req, res) => {
  try {
    const due = await PaymentDue.findById(req.params.id)
      .populate('familyId', 'headName slNo')
      .populate('paymentTypeId', 'name');
    
    if (!due) {
      return res.status(404).json({ message: 'Payment due not found' });
    }
    
    // Get related payments
    const payments = await Payment.find({ 
      dueId: due._id 
    }).sort({ paymentDate: -1 });
    
    // Add payments to the response
    const dueWithPayments = {
      ...due.toObject(),
      payments
    };
    
    res.json(dueWithPayments);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Record payment for a due
router.post('/record', authenticate, async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { 
      dueId, 
      amount, 
      paymentDate, 
      receiptNumber,
      paymentMethod,
      notes,
      isPartialPayment
    } = req.body;
    
    // Validate due exists
    const due = await PaymentDue.findById(dueId).session(session);
    if (!due) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({ message: 'Payment due not found' });
    }
    
    // Check if due is already paid
    if (due.isPaid) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: 'This payment due is already fully paid' });
    }
    
    // Calculate remaining amount
    const remainingAmount = due.amount - (due.partialPayment || 0);
    
    // Validate payment amount
    if (amount <= 0) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: 'Payment amount must be greater than zero' });
    }
    
    if (!isPartialPayment && amount > remainingAmount) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ 
        message: 'Payment amount cannot exceed the remaining due amount unless marked as partial payment' 
      });
    }
    
    // Generate receipt number if not provided
    let finalReceiptNumber = receiptNumber;
    if (!finalReceiptNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      
      // Get count of payments for this month to generate sequential number
      const paymentCount = await Payment.countDocuments({
        createdAt: {
          $gte: new Date(date.getFullYear(), date.getMonth(), 1),
          $lt: new Date(date.getFullYear(), date.getMonth() + 1, 1)
        }
      });
      
      finalReceiptNumber = `RCP-${year}${month}-${(paymentCount + 1).toString().padStart(3, '0')}`;
    }
    
    // Create payment record
    const payment = new Payment({
      dueId,
      familyId: due.familyId,
      paymentTypeId: due.paymentTypeId,
      amount: parseFloat(amount as any),
      paymentDate: new Date(paymentDate),
      year: due.year,
      period: due.period,
      receiptNumber: finalReceiptNumber,
      paymentMethod,
      notes,
      collectedBy: req.user._id
    });
    
    await payment.save({ session });
    
    // Update due status
    if (isPartialPayment || parseFloat(amount as any) < remainingAmount) {
      // Partial payment
      due.partialPayment = (due.partialPayment || 0) + parseFloat(amount as any);
      due.isPaid = false;
    } else {
      // Full payment
      due.partialPayment = due.amount;
      due.isPaid = true;
    }
    
    await due.save({ session });
    
    await session.commitTransaction();
    session.endSession();
    
    res.status(201).json(payment);
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json({ message: 'Server error', error });
  }
});

// Generate payment dues for all families
router.post(
  '/generate-dues',
  authenticate,
  (req, res, next) => {
    // Check if user is admin
    if (req.user && req.user.role === 'Admin') {
      next();
    } else {
      return res.status(403).json({ message: 'Unauthorized: Admin role required' });
    }
  },
  async (req, res) => {
    try {
      const { paymentTypeId, year, dueDate, familyFilter, wardFilter } = req.body;
      
      console.log('Generate dues request body:', req.body);
      
      // Validate required fields
      if (!paymentTypeId) {
        return res.status(400).json({ message: 'Payment type is required' });
      }
      
      if (!year) {
        return res.status(400).json({ message: 'Year is required' });
      }
      
      if (!dueDate) {
        return res.status(400).json({ message: 'Due date is required' });
      }
      
      // Validate payment type
      const paymentType = await PaymentType.findById(paymentTypeId);
      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }

      // Build query for families
      const familyQuery: any = {};

      if (familyFilter) {
        familyQuery._id = familyFilter;
      }

      if (wardFilter) {
        familyQuery.$or = [
          { wardP: wardFilter },
          { wardC: wardFilter }
        ];
      }

      // Get all matching families
      const families = await Family.find(familyQuery);

      if (families.length === 0) {
        return res.status(400).json({ message: 'No matching families found' });
      }

      // Determine years to generate dues for based on payment type configuration
      let yearsToGenerate = [parseInt(year.toString())];

      if (paymentType.frequency === 'yearly' && paymentType.startYear && paymentType.endYear) {
        // For yearly payment types with date ranges, generate for all years in range
        yearsToGenerate = [];
        for (let y = paymentType.startYear; y <= paymentType.endYear; y++) {
          yearsToGenerate.push(y);
        }
        console.log(`Generating dues for yearly payment type from ${paymentType.startYear} to ${paymentType.endYear}:`, yearsToGenerate);
      } else if (paymentType.frequency === 'monthly' && paymentType.startYear && paymentType.endYear) {
        // For monthly payment types with date ranges, generate for all months in range
        yearsToGenerate = [];
        for (let y = paymentType.startYear; y <= paymentType.endYear; y++) {
          yearsToGenerate.push(y);
        }
        console.log(`Generating dues for monthly payment type from ${paymentType.startYear} to ${paymentType.endYear}:`, yearsToGenerate);
      }

      // Create payment dues for all years and families
      const dues = [];
      for (const targetYear of yearsToGenerate) {
        // Check for existing dues for this year to avoid duplicates
        const existingDues = await PaymentDue.find({
          paymentTypeId,
          year: targetYear,
        });

        const existingFamilyIds = existingDues.map(due => due.familyId.toString());

        for (const family of families) {
          // Skip if family already has this due for this year
          if (existingFamilyIds.includes((family._id as mongoose.Types.ObjectId).toString())) {
            continue;
          }

          // Calculate amount based on frequency and date range
          let calculatedAmount = paymentType.amount;

          // For yearly fees with date ranges, the amount is per year
          if (paymentType.frequency === 'yearly' && paymentType.startYear && paymentType.endYear) {
            // Amount is already per year, so use as-is
            calculatedAmount = paymentType.amount;
          } else if (paymentType.frequency === 'monthly' && paymentType.startYear && paymentType.endYear && paymentType.startMonth && paymentType.endMonth) {
            // For monthly fees, calculate based on number of months
            const totalMonths = (paymentType.endYear - paymentType.startYear) * 12 + (paymentType.endMonth - paymentType.startMonth + 1);
            calculatedAmount = paymentType.amount * totalMonths;
          }

          const dueData: any = {
            familyId: family._id,
            paymentTypeId,
            year: targetYear,
            amount: calculatedAmount,
            dueDate: new Date(dueDate),
            isPaid: false,
            partialPayment: 0
          };

          // Add date range fields if payment type has them
          if (paymentType.startYear) dueData.startYear = paymentType.startYear;
          if (paymentType.endYear) dueData.endYear = paymentType.endYear;
          if (paymentType.startMonth) dueData.startMonth = paymentType.startMonth;
          if (paymentType.endMonth) dueData.endMonth = paymentType.endMonth;
          if (paymentType.frequency === 'yearly' || paymentType.frequency === 'monthly') {
            dueData.coversMultiplePeriods = true;
          }

          const due = new PaymentDue(dueData);
          await due.save();
          dues.push(due);
        }
      }
      
      res.status(201).json(dues);
    } catch (error) {
      console.error('Error generating dues:', error);
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      res.status(500).json({ message: 'Server error', error: errorMessage });
    }
  }
);

// Get pending dues by family
router.get('/pending/family/:familyId', authenticate, async (req, res) => {
  try {
    const dues = await PaymentDue.find({
      familyId: req.params.familyId,
      isPaid: false
    })
    .populate('paymentTypeId', 'name startYear endYear startMonth endMonth frequency')
    .sort({ dueDate: 1 });

    res.json(dues);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get related dues for a payment type and date range
router.get('/related-dues/:familyId/:paymentTypeId', authenticate, async (req, res) => {
  try {
    const { familyId, paymentTypeId } = req.params;
    const { startYear, endYear, startMonth, endMonth } = req.query;

    // Get the payment type to understand its frequency
    const paymentType = await PaymentType.findById(paymentTypeId);
    if (!paymentType) {
      return res.status(404).json({ message: 'Payment type not found' });
    }

    let query: any = {
      familyId,
      paymentTypeId,
      isPaid: false
    };

    // Build query based on date range
    if (paymentType.frequency === 'yearly' && startYear && endYear) {
      query.year = {
        $gte: parseInt(startYear as string),
        $lte: parseInt(endYear as string)
      };
    } else if (paymentType.frequency === 'monthly' && startYear && endYear) {
      // For monthly, we need to handle year-month combinations
      const start = parseInt(startYear as string);
      const end = parseInt(endYear as string);
      const startM = startMonth ? parseInt(startMonth as string) : 1;
      const endM = endMonth ? parseInt(endMonth as string) : 12;

      if (start === end) {
        // Same year, filter by month range
        query.year = start;
        query.$or = [
          { startMonth: { $gte: startM, $lte: endM } },
          { endMonth: { $gte: startM, $lte: endM } },
          { $and: [{ startMonth: { $lte: startM } }, { endMonth: { $gte: endM } }] }
        ];
      } else {
        // Multiple years
        query.year = { $gte: start, $lte: end };
      }
    }

    const relatedDues = await PaymentDue.find(query)
      .populate('paymentTypeId', 'name frequency')
      .sort({ year: 1, startMonth: 1, dueDate: 1 });

    res.json(relatedDues);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment summary by year
router.get('/summary/year/:year', authenticate, async (req, res) => {
  try {
    const year = parseInt(req.params.year);
    
    // Get all payment types
    const paymentTypes = await PaymentType.find();
    
    // For each payment type, get summary data
    const summaryData = await Promise.all(
      paymentTypes.map(async (type) => {
        // Count total dues for this payment type and year
        const dueCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year
        });
        
        // Count paid dues
        const paidCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: true
        });
        
        // Count pending dues
        const pendingCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false
        });
        
        // Count overdue dues (due date is in the past)
        const overdueCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false,
          dueDate: { $lt: new Date() }
        });
        
        // Count partial payments
        const partialCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false,
          partialPayment: { $gt: 0 }
        });
        
        // Calculate total amount
        const duesAggregate = await PaymentDue.aggregate([
          {
            $match: {
              paymentTypeId: type._id,
              year
            }
          },
          {
            $group: {
              _id: null,
              totalAmount: { $sum: '$amount' },
              paidAmount: {
                $sum: {
                  $cond: [
                    { $eq: ['$isPaid', true] },
                    '$amount',
                    { $ifNull: ['$partialPayment', 0] }
                  ]
                }
              }
            }
          }
        ]);
        
        const totalAmount = duesAggregate.length > 0 ? duesAggregate[0].totalAmount : 0;
        const paidAmount = duesAggregate.length > 0 ? duesAggregate[0].paidAmount : 0;
        
        return {
          paymentType: {
            _id: type._id,
            name: type.name
          },
          dueCount,
          paidCount,
          pendingCount,
          overdueCount,
          partialCount,
          totalAmount,
          paidAmount,
          pendingAmount: totalAmount - paidAmount
        };
      })
    );
    
    res.json(summaryData);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get unpaid dues summary by year (optimized for dues summary page)
router.get('/unpaid-summary/year/:year', authenticate, async (req, res) => {
  try {
    const year = parseInt(req.params.year);

    // Get all payment types
    const paymentTypes = await PaymentType.find({ isActive: true });

    // For each payment type, get only unpaid dues data
    const summaryData = await Promise.all(
      paymentTypes.map(async (type) => {
        // Count only unpaid dues for this payment type and year
        const pendingCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false
        });

        // Count overdue dues (due date is in the past)
        const overdueCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false,
          dueDate: { $lt: new Date() }
        });

        // Count partial payments
        const partialCount = await PaymentDue.countDocuments({
          paymentTypeId: type._id,
          year,
          isPaid: false,
          partialPayment: { $gt: 0 }
        });

        // Calculate pending amounts only
        const pendingAggregate = await PaymentDue.aggregate([
          {
            $match: {
              paymentTypeId: type._id,
              year,
              isPaid: false
            }
          },
          {
            $group: {
              _id: null,
              totalPendingAmount: { $sum: '$amount' },
              partiallyPaidAmount: { $sum: { $ifNull: ['$partialPayment', 0] } }
            }
          }
        ]);

        const totalPendingAmount = pendingAggregate.length > 0 ? pendingAggregate[0].totalPendingAmount : 0;
        const partiallyPaidAmount = pendingAggregate.length > 0 ? pendingAggregate[0].partiallyPaidAmount : 0;
        const actualPendingAmount = totalPendingAmount - partiallyPaidAmount;

        // Only return data if there are pending dues
        if (pendingCount > 0) {
          return {
            paymentType: {
              _id: type._id,
              name: type.name
            },
            pendingCount,
            overdueCount,
            partialCount,
            totalPendingAmount,
            actualPendingAmount
          };
        }
        return null;
      })
    );

    // Filter out null entries (payment types with no pending dues)
    const filteredData = summaryData.filter(item => item !== null);

    res.json(filteredData);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Preview payment dues generation
router.post(
  '/preview-dues',
  authenticate,
  (req, res, next) => {
    // Check if user is admin
    if (req.user && req.user.role === 'Admin') {
      next();
    } else {
      return res.status(403).json({ message: 'Unauthorized: Admin role required' });
    }
  },
  async (req, res) => {
    try {
      const { paymentTypeId, year, dueDate, familyFilter, wardFilter } = req.body;
      
      console.log('Preview dues request body:', req.body);
      
      // Validate required fields
      if (!paymentTypeId) {
        return res.status(400).json({ message: 'Payment type is required' });
      }
      
      if (!year) {
        return res.status(400).json({ message: 'Year is required' });
      }
      
      if (!dueDate) {
        return res.status(400).json({ message: 'Due date is required' });
      }
      
      // Validate payment type
      const paymentType = await PaymentType.findById(paymentTypeId);
      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }

      // Build query for families
      const familyQuery: any = {};

      if (familyFilter) {
        familyQuery._id = familyFilter;
      }

      if (wardFilter) {
        familyQuery.$or = [
          { wardP: wardFilter },
          { wardC: wardFilter }
        ];
      }

      // Get all matching families
      const families = await Family.find(familyQuery);

      if (families.length === 0) {
        return res.status(400).json({ message: 'No matching families found' });
      }

      // Determine years to generate dues for based on payment type configuration
      let yearsToGenerate = [parseInt(year.toString())];

      if (paymentType.frequency === 'yearly' && paymentType.startYear && paymentType.endYear) {
        // For yearly payment types with date ranges, generate for all years in range
        yearsToGenerate = [];
        for (let y = paymentType.startYear; y <= paymentType.endYear; y++) {
          yearsToGenerate.push(y);
        }
      } else if (paymentType.frequency === 'monthly' && paymentType.startYear && paymentType.endYear) {
        // For monthly payment types with date ranges, generate for all months in range
        yearsToGenerate = [];
        for (let y = paymentType.startYear; y <= paymentType.endYear; y++) {
          yearsToGenerate.push(y);
        }
      }

      // Create preview of dues for all years and families
      const previewDues = [];
      for (const targetYear of yearsToGenerate) {
        // Check for existing dues for this year
        const existingDues = await PaymentDue.find({
          paymentTypeId,
          year: targetYear,
        });

        const existingFamilyIds = existingDues.map(due => due.familyId.toString());

        for (const family of families) {
          const isExisting = existingFamilyIds.includes((family._id as mongoose.Types.ObjectId).toString());

          previewDues.push({
            familyId: {
              _id: family._id,
              headName: family.headName,
              slNo: family.slNo
            },
            year: targetYear,
            amount: paymentType.amount,
            dueDate,
            status: isExisting ? 'existing' : 'new'
          });
        }
      }
      
      res.json(previewDues);
    } catch (error) {
      console.error('Error previewing dues:', error);
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      res.status(500).json({ message: 'Server error', error: errorMessage });
    }
  }
);

// Search for dues
router.get('/search-dues', authenticate, async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query || (query as string).length < 2) {
      return res.json([]);
    }
    
    // Search for dues by family name or payment type
    const dues = await PaymentDue.find({
      $or: [
        { 'familyId.headName': { $regex: query, $options: 'i' } },
        { 'familyId.slNo': { $regex: query, $options: 'i' } },
        { 'paymentTypeId.name': { $regex: query, $options: 'i' } }
      ]
    })
    .populate('familyId', 'headName slNo')
    .populate('paymentTypeId', 'name')
    .limit(10);
    
    res.json(dues);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create a new payment directly (not tied to a due)
router.post('/', authenticate, async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { 
      familyId, 
      paymentTypeId, 
      amount, 
      paymentDate, 
      year,
      period,
      receiptNumber,
      paymentMethod,
      notes,
      isPartialPayment,
      createDueForRemaining,
      remainingAmount
    } = req.body;
    
    // Generate receipt number if not provided
    let finalReceiptNumber = receiptNumber;
    if (!finalReceiptNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      
      // Get count of payments for this month to generate sequential number
      const paymentCount = await Payment.countDocuments({
        createdAt: {
          $gte: new Date(date.getFullYear(), date.getMonth(), 1),
          $lt: new Date(date.getFullYear(), date.getMonth() + 1, 1)
        }
      });
      
      finalReceiptNumber = `RCP-${year}${month}-${(paymentCount + 1).toString().padStart(3, '0')}`;
    }
    
    // Create payment record
    const payment = new Payment({
      familyId,
      paymentTypeId,
      amount: parseFloat(amount as any),
      paymentDate: new Date(paymentDate),
      year,
      period,
      receiptNumber: finalReceiptNumber,
      paymentMethod,
      notes,
      collectedBy: req.user._id
    });
    
    await payment.save({ session });
    
    // If this is a partial payment and we need to create a due for the remaining amount
    if (isPartialPayment && createDueForRemaining && remainingAmount > 0) {
      // Create a new due for the remaining amount
      const dueData: any = {
        familyId,
        paymentTypeId,
        amount: parseFloat(remainingAmount as any),
        dueDate: req.body.dueDate ? new Date(req.body.dueDate) : new Date(),
        year,
        isPaid: false,
        partialPayment: 0
      };
      
      // Only add period if it's not empty
      if (period && period.trim() !== '') {
        dueData.period = period;
      }
      
      const due = new PaymentDue(dueData);
      await due.save({ session });

      // Create response object with the created due ID
      const response = {
        ...payment.toObject(),
        createdDueId: due._id
      };

      await session.commitTransaction();
      session.endSession();

      res.status(201).json(response);
    } else {
      await session.commitTransaction();
      session.endSession();

      res.status(201).json(payment);
    }
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get all payments with filtering
router.get('/', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    
    const query: any = {};
    
    // Filter by family
    if (req.query.familyId) {
      query.familyId = req.query.familyId;
    }
    
    // Filter by payment type
    if (req.query.paymentTypeId) {
      query.paymentTypeId = req.query.paymentTypeId;
    }
    
    // Filter by year
    if (req.query.year) {
      query.year = parseInt(req.query.year as string);
    }
    
    // Filter by payment method
    if (req.query.paymentMethod) {
      query.paymentMethod = req.query.paymentMethod;
    }
    
    // Filter by payment date range
    if (req.query.startDate && req.query.endDate) {
      query.paymentDate = {
        $gte: new Date(req.query.startDate as string),
        $lte: new Date(req.query.endDate as string)
      };
    }
    
    // Handle search
    if (req.query.search) {
      // First get matching families
      const searchRegex = new RegExp(req.query.search as string, 'i');
      const matchingFamilies = await Family.find({
        $or: [
          { headName: searchRegex },
          { slNo: searchRegex }
        ]
      }).select('_id');
      
      const familyIds = matchingFamilies.map(f => f._id);
      
      // Also get matching payment types
      const matchingTypes = await PaymentType.find({
        name: searchRegex
      }).select('_id');
      
      const typeIds = matchingTypes.map(t => t._id);
      
      // Add to query
      if (familyIds.length > 0 || typeIds.length > 0) {
        query.$or = [];
        
        if (familyIds.length > 0) {
          query.$or.push({ familyId: { $in: familyIds } });
        }
        
        if (typeIds.length > 0) {
          query.$or.push({ paymentTypeId: { $in: typeIds } });
        }
      } else if (req.query.search) {
        // No matches found, return empty result
        return res.json({
          payments: [],
          pagination: {
            total: 0,
            page,
            pages: 0
          }
        });
      }
    }
    
    const total = await Payment.countDocuments(query);
    
    const payments = await Payment.find(query)
      .populate('familyId', 'headName slNo')
      .populate('paymentTypeId', 'name')
      .populate('collectedBy', 'name')
      .sort({ paymentDate: -1 })
      .skip(skip)
      .limit(limit);
    
    res.json({
      payments,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('familyId', 'headName slNo')
      .populate('paymentTypeId', 'name')
      .populate('collectedBy', 'name');
    
    if (!payment) {
      return res.status(404).json({ message: 'Payment not found' });
    }
    
    res.json(payment);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Update payment
router.put(
  '/:id',
  authenticate,
  authorize(['Admin', 'Editor']),
  async (req, res) => {
    try {
      const {
        amount,
        paymentDate,
        receiptNumber,
        paymentMethod,
        notes
      } = req.body;

      // Find the payment
      const payment = await Payment.findById(req.params.id);

      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }

      // Update payment fields
      if (amount !== undefined) payment.amount = parseFloat(amount as any);
      if (paymentDate !== undefined) payment.paymentDate = new Date(paymentDate);
      if (receiptNumber !== undefined) payment.receiptNumber = receiptNumber;
      if (paymentMethod !== undefined) payment.paymentMethod = paymentMethod;
      if (notes !== undefined) payment.notes = notes;

      await payment.save();

      // Populate the response
      const updatedPayment = await Payment.findById(payment._id)
        .populate('familyId', 'headName slNo')
        .populate('paymentTypeId', 'name')
        .populate('collectedBy', 'name');

      res.json(updatedPayment);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete payment
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find the payment
      const payment = await Payment.findById(req.params.id).session(session);

      if (!payment) {
        await session.abortTransaction();
        session.endSession();
        return res.status(404).json({ message: 'Payment not found' });
      }

      // If this payment was linked to a due, update the due status
      if (payment.dueId) {
        const due = await PaymentDue.findById(payment.dueId).session(session);

        if (due) {
          // Subtract this payment amount from the due's partial payment
          due.partialPayment = Math.max(0, (due.partialPayment || 0) - payment.amount);

          // Update due status
          if (due.partialPayment >= due.amount) {
            due.isPaid = true;
          } else {
            due.isPaid = false;
          }

          await due.save({ session });
        }
      }

      // Delete the payment
      await Payment.findByIdAndDelete(req.params.id).session(session);

      await session.commitTransaction();
      session.endSession();

      res.json({ message: 'Payment deleted successfully' });
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;
