import express, { Router } from 'express';
import Settings from '../models/Settings.ts';
import { authenticate, authorize } from '../middleware/auth.ts';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router: Router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/settings');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const filetypes = /jpeg|jpg|png/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only .png, .jpg and .jpeg format allowed!'));
  }
});

// Get settings
router.get('/', authenticate, async (req, res) => {
  try {
    // Get the first settings document or create one if it doesn't exist
    let settings = await Settings.findOne();
    
    if (!settings) {
      settings = new Settings({
        parishName: 'My Parish',
        parishAddress: 'Address',
        parishPhone: 'Phone',
        parishEmail: '<EMAIL>',
        emailNotifications: true,
        backupFrequency: 'daily',
        theme: {
          primary: '#4f46e5',
          secondary: '#8b5cf6',
          accent: '#ec4899'
        }
      });
      await settings.save();
    }
    
    res.json(settings);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Update settings
router.put(
  '/',
  authenticate,
  authorize(['Admin']),
  upload.fields([
    { name: 'logo', maxCount: 1 },
    { name: 'banner', maxCount: 1 },
    { name: 'church', maxCount: 1 },
    { name: 'gallery', maxCount: 10 }
  ]),
  async (req, res) => {
    try {
      const settingsData = JSON.parse(req.body.settingsData);
      
      // Handle file uploads
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      
      if (files.logo) {
        settingsData.logo = `/uploads/settings/${files.logo[0].filename}`;
      }
      
      if (files.banner) {
        settingsData.banner = `/uploads/settings/${files.banner[0].filename}`;
      }
      
      if (files.church) {
        settingsData.church = `/uploads/settings/${files.church[0].filename}`;
      }
      
      if (files.gallery) {
        settingsData.gallery = files.gallery.map(file => `/uploads/settings/${file.filename}`);
      }
      
      // Find the first settings document or create one if it doesn't exist
      let settings = await Settings.findOne();
      
      if (!settings) {
        settings = new Settings(settingsData);
        await settings.save();
      } else {
        // Update existing settings
        settings = await Settings.findByIdAndUpdate(
          settings._id,
          settingsData,
          { new: true }
        );
      }
      
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update theme settings
router.put(
  '/theme',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const { theme } = req.body;
      
      // Find the first settings document or create one if it doesn't exist
      let settings = await Settings.findOne();
      
      if (!settings) {
        settings = new Settings({
          parishName: 'My Parish',
          parishAddress: 'Address',
          parishPhone: 'Phone',
          parishEmail: '<EMAIL>',
          emailNotifications: true,
          backupFrequency: 'daily',
          theme
        });
        await settings.save();
      } else {
        // Update existing theme settings
        settings.theme = theme;
        await settings.save();
      }
      
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;