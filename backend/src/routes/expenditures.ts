import express from 'express';
import mongoose from 'mongoose';
import Expenditure from '../models/Expenditure.ts';
import Payment from '../models/Payment.ts';
import Offering from '../models/Offering.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router = express.Router();

// Get all expenditures with filtering and pagination
router.get('/', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;
    
    const query: any = {};
    
    // Filter by category
    if (req.query.category) {
      query.category = req.query.category;
    }
    
    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Filter by date range
    if (req.query.startDate || req.query.endDate) {
      query.date = {};
      if (req.query.startDate) {
        query.date.$gte = new Date(req.query.startDate as string);
      }
      if (req.query.endDate) {
        query.date.$lte = new Date(req.query.endDate as string);
      }
    }
    
    // Filter by year
    if (req.query.year) {
      const year = parseInt(req.query.year as string);
      query.date = {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1)
      };
    }
    
    const expenditures = await Expenditure.find(query)
      .populate('createdBy', 'name email')
      .populate('approvedBy', 'name email')
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Expenditure.countDocuments(query);
    
    res.json({
      expenditures,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get expenditure by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const expenditure = await Expenditure.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('approvedBy', 'name email');
    
    if (!expenditure) {
      return res.status(404).json({ message: 'Expenditure not found' });
    }
    
    res.json(expenditure);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new expenditure
router.post('/', authenticate, async (req, res) => {
  try {
    const {
      description,
      amount,
      category,
      date,
      receiptNumber,
      vendor,
      notes,
      attachments
    } = req.body;
    
    const expenditure = new Expenditure({
      description,
      amount: parseFloat(amount),
      category,
      date: date ? new Date(date) : new Date(),
      receiptNumber,
      vendor,
      notes,
      attachments: attachments || [],
      createdBy: req.user._id,
      status: 'pending'
    });
    
    await expenditure.save();
    
    // Populate the created expenditure
    await expenditure.populate('createdBy', 'name email');
    
    res.status(201).json(expenditure);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Update expenditure
router.put('/:id', authenticate, async (req, res) => {
  try {
    const expenditure = await Expenditure.findById(req.params.id);
    
    if (!expenditure) {
      return res.status(404).json({ message: 'Expenditure not found' });
    }
    
    // Check if user can edit this expenditure
    if (expenditure.createdBy.toString() !== req.user._id.toString() && req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Not authorized to edit this expenditure' });
    }
    
    // Don't allow editing approved expenditures unless admin
    if (expenditure.status === 'approved' && req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Cannot edit approved expenditures' });
    }
    
    const {
      description,
      amount,
      category,
      date,
      receiptNumber,
      vendor,
      notes,
      attachments
    } = req.body;
    
    expenditure.description = description || expenditure.description;
    expenditure.amount = amount ? parseFloat(amount) : expenditure.amount;
    expenditure.category = category || expenditure.category;
    expenditure.date = date ? new Date(date) : expenditure.date;
    expenditure.receiptNumber = receiptNumber !== undefined ? receiptNumber : expenditure.receiptNumber;
    expenditure.vendor = vendor !== undefined ? vendor : expenditure.vendor;
    expenditure.notes = notes !== undefined ? notes : expenditure.notes;
    expenditure.attachments = attachments !== undefined ? attachments : expenditure.attachments;
    
    await expenditure.save();
    
    await expenditure.populate('createdBy', 'name email');
    await expenditure.populate('approvedBy', 'name email');
    
    res.json(expenditure);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Approve/Reject expenditure (Admin only)
router.patch('/:id/status', authenticate, authorize(['Admin']), async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status. Must be approved or rejected' });
    }
    
    const expenditure = await Expenditure.findById(req.params.id);
    
    if (!expenditure) {
      return res.status(404).json({ message: 'Expenditure not found' });
    }
    
    expenditure.status = status;
    if (status === 'approved') {
      expenditure.approvedBy = req.user._id;
    }
    
    await expenditure.save();
    
    await expenditure.populate('createdBy', 'name email');
    await expenditure.populate('approvedBy', 'name email');
    
    res.json(expenditure);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Delete expenditure
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const expenditure = await Expenditure.findById(req.params.id);
    
    if (!expenditure) {
      return res.status(404).json({ message: 'Expenditure not found' });
    }
    
    // Check if user can delete this expenditure
    if (expenditure.createdBy.toString() !== req.user._id.toString() && req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Not authorized to delete this expenditure' });
    }
    
    // Don't allow deleting approved expenditures unless admin
    if (expenditure.status === 'approved' && req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Cannot delete approved expenditures' });
    }
    
    await Expenditure.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Expenditure deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get expenditure summary by category
router.get('/summary/category', authenticate, async (req, res) => {
  try {
    const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();
    
    const summary = await Expenditure.aggregate([
      {
        $match: {
          status: 'approved',
          date: {
            $gte: new Date(year, 0, 1),
            $lt: new Date(year + 1, 0, 1)
          }
        }
      },
      {
        $group: {
          _id: '$category',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { totalAmount: -1 }
      }
    ]);
    
    res.json(summary);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get financial balance (total income - total expenditures)
router.get('/balance/summary', authenticate, async (req, res) => {
  try {
    const year = req.query.year ? parseInt(req.query.year as string) : new Date().getFullYear();
    
    // Get total payments for the year
    const paymentsTotal = await Payment.aggregate([
      {
        $match: {
          paymentDate: {
            $gte: new Date(year, 0, 1),
            $lt: new Date(year + 1, 0, 1)
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    // Get total offerings for the year
    const offeringsTotal = await Offering.aggregate([
      {
        $match: {
          date: {
            $gte: new Date(year, 0, 1),
            $lt: new Date(year + 1, 0, 1)
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    // Get total approved expenditures for the year
    const expendituresTotal = await Expenditure.aggregate([
      {
        $match: {
          status: 'approved',
          date: {
            $gte: new Date(year, 0, 1),
            $lt: new Date(year + 1, 0, 1)
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    const totalIncome = (paymentsTotal[0]?.total || 0) + (offeringsTotal[0]?.total || 0);
    const totalExpenses = expendituresTotal[0]?.total || 0;
    const balance = totalIncome - totalExpenses;
    
    res.json({
      year,
      income: {
        payments: paymentsTotal[0]?.total || 0,
        offerings: offeringsTotal[0]?.total || 0,
        total: totalIncome
      },
      expenses: {
        total: totalExpenses
      },
      balance
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

export default router;
