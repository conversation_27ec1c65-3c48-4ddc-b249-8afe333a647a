import express, { Router } from 'express';
import PaymentType from '../models/PaymentType.ts';
import { authenticate, authorize } from '../middleware/auth.ts';

const router: Router = express.Router();

// Get all payment types
router.get('/', authenticate, async (req, res) => {
  try {
    const paymentTypes = await PaymentType.find().sort({ name: 1 });
    res.json(paymentTypes);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment type by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const paymentType = await PaymentType.findById(req.params.id);

    if (!paymentType) {
      return res.status(404).json({ message: 'Payment type not found' });
    }

    res.json(paymentType);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get payment type by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const paymentType = await PaymentType.findById(req.params.id);
    if (!paymentType) {
      return res.status(404).json({ message: 'Payment type not found' });
    }
    res.json(paymentType);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new payment type
router.post(
  '/',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const {
        name,
        description,
        amount,
        frequency,
        startYear,
        endYear,
        startMonth,
        endMonth
      } = req.body;

      // Check if payment type already exists
      const existingType = await PaymentType.findOne({ name });
      if (existingType) {
        return res.status(400).json({ message: 'Payment type already exists' });
      }

      // Validate date range fields based on frequency
      if (frequency === 'yearly' || frequency === 'monthly') {
        if (!startYear || !endYear) {
          return res.status(400).json({
            message: 'Start year and end year are required for yearly and monthly payment types'
          });
        }
        if (endYear < startYear) {
          return res.status(400).json({
            message: 'End year must be greater than or equal to start year'
          });
        }
      }

      if (frequency === 'monthly') {
        if (!startMonth || !endMonth) {
          return res.status(400).json({
            message: 'Start month and end month are required for monthly payment types'
          });
        }
        if (startYear === endYear && endMonth < startMonth) {
          return res.status(400).json({
            message: 'End month must be greater than or equal to start month when in the same year'
          });
        }
      }

      const paymentTypeData: any = {
        name,
        description,
        amount,
        frequency
      };

      // Add date range fields if provided
      if (startYear) paymentTypeData.startYear = startYear;
      if (endYear) paymentTypeData.endYear = endYear;
      if (startMonth) paymentTypeData.startMonth = startMonth;
      if (endMonth) paymentTypeData.endMonth = endMonth;

      const paymentType = new PaymentType(paymentTypeData);
      await paymentType.save();

      res.status(201).json(paymentType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update payment type
router.put(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const {
        name,
        description,
        amount,
        frequency,
        isActive,
        startYear,
        endYear,
        startMonth,
        endMonth
      } = req.body;

      // Validate date range fields based on frequency
      if (frequency === 'yearly' || frequency === 'monthly') {
        if (!startYear || !endYear) {
          return res.status(400).json({
            message: 'Start year and end year are required for yearly and monthly payment types'
          });
        }
        if (endYear < startYear) {
          return res.status(400).json({
            message: 'End year must be greater than or equal to start year'
          });
        }
      }

      if (frequency === 'monthly') {
        if (!startMonth || !endMonth) {
          return res.status(400).json({
            message: 'Start month and end month are required for monthly payment types'
          });
        }
        if (startYear === endYear && endMonth < startMonth) {
          return res.status(400).json({
            message: 'End month must be greater than or equal to start month when in the same year'
          });
        }
      }

      const updateData: any = {
        name,
        description,
        amount,
        frequency,
        isActive
      };

      // Add date range fields if provided
      if (startYear !== undefined) updateData.startYear = startYear;
      if (endYear !== undefined) updateData.endYear = endYear;
      if (startMonth !== undefined) updateData.startMonth = startMonth;
      if (endMonth !== undefined) updateData.endMonth = endMonth;

      const paymentType = await PaymentType.findByIdAndUpdate(
        req.params.id,
        updateData,
        { new: true }
      );

      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }

      res.json(paymentType);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete payment type
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const paymentType = await PaymentType.findByIdAndDelete(req.params.id);
      
      if (!paymentType) {
        return res.status(404).json({ message: 'Payment type not found' });
      }
      
      res.json({ message: 'Payment type deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;