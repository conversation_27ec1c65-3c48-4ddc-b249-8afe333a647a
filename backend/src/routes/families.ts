import express, { Router } from 'express';
import Family from '../models/Family.ts';
import { authenticate, authorize } from '../middleware/auth.ts';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const router: Router = express.Router();

// Create __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req: express.Request, file: Express.Multer.File, cb: (error: Error | null, destination: string) => void) => {
    const uploadDir = path.join(__dirname, '../../uploads/families');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req: express.Request, file: Express.Multer.File, cb: (error: Error | null, filename: string) => void) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req: express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    const filetypes = /jpeg|jpg|png/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only .png, .jpg and .jpeg format allowed!') as Error);
  }
});

// Get all families
router.get('/', authenticate, async (req, res) => {
  try {
    const families = await Family.find().sort({ createdAt: -1 });
    res.json(families);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Get family by ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const family = await Family.findById(req.params.id);
    
    if (!family) {
      return res.status(404).json({ message: 'Family not found' });
    }
    
    res.json(family);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
});

// Create new family
router.post(
  '/',
  authenticate,
  authorize(['Admin', 'Editor']),
  upload.fields([
    { name: 'headPhoto', maxCount: 1 },
    { name: 'spousePhoto', maxCount: 1 },
    { name: 'memberPhotos', maxCount: 10 }
  ]),
  async (req, res) => {
    try {
      const familyData = JSON.parse(req.body.familyData);
      
      // Handle file uploads
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      
      if (files.headPhoto) {
        familyData.headPhoto = `/uploads/families/${files.headPhoto[0].filename}`;
      }
      
      if (files.spousePhoto) {
        familyData.spousePhoto = `/uploads/families/${files.spousePhoto[0].filename}`;
      }
      
      if (files.memberPhotos && familyData.members) {
        files.memberPhotos.forEach((file, index) => {
          if (familyData.members[index]) {
            familyData.members[index].photo = `/uploads/families/${file.filename}`;
          }
        });
      }
      
      const family = new Family(familyData);
      await family.save();
      
      res.status(201).json(family);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Update family
router.put(
  '/:id',
  authenticate,
  authorize(['Admin', 'Editor']),
  upload.fields([
    { name: 'headPhoto', maxCount: 1 },
    { name: 'spousePhoto', maxCount: 1 },
    { name: 'memberPhotos', maxCount: 10 }
  ]),
  async (req, res) => {
    try {
      const familyData = JSON.parse(req.body.familyData);
      
      // Handle file uploads
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      
      if (files.headPhoto) {
        familyData.headPhoto = `/uploads/families/${files.headPhoto[0].filename}`;
      }
      
      if (files.spousePhoto) {
        familyData.spousePhoto = `/uploads/families/${files.spousePhoto[0].filename}`;
      }
      
      if (files.memberPhotos && familyData.members) {
        files.memberPhotos.forEach((file, index) => {
          if (familyData.members[index]) {
            familyData.members[index].photo = `/uploads/families/${file.filename}`;
          }
        });
      }
      
      const family = await Family.findByIdAndUpdate(
        req.params.id,
        familyData,
        { new: true }
      );
      
      if (!family) {
        return res.status(404).json({ message: 'Family not found' });
      }
      
      res.json(family);
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

// Delete family
router.delete(
  '/:id',
  authenticate,
  authorize(['Admin']),
  async (req, res) => {
    try {
      const family = await Family.findByIdAndDelete(req.params.id);
      
      if (!family) {
        return res.status(404).json({ message: 'Family not found' });
      }
      
      res.json({ message: 'Family deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Server error', error });
    }
  }
);

export default router;
