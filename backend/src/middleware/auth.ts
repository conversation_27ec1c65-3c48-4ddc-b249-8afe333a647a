import * as express from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User.ts';


type Request = express.Request;
type Response = express.Response;
type NextFunction = express.NextFunction;

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: any; // Temporarily use 'any' until we fix the proper type
    }
  }
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret') as { id: string };
    const user = await User.findById(decoded.id);
    
    if (!user || user.status !== 'Active') {
      return res.status(401).json({ message: 'User not found or inactive' });
    }
    
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Invalid token' });
  }
};

export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    next();
  };
};
