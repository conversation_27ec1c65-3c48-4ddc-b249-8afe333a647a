import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import authRoutes from './routes/auth.ts';
import familyRoutes from './routes/families.ts';
import offeringRoutes from './routes/offerings.ts';
import offeringTypeRoutes from './routes/offeringTypes.ts';
import eventRoutes from './routes/events.ts';
import userRoutes from './routes/users.ts';
import settingsRoutes from './routes/settings.ts';
import paymentTypeRoutes from './routes/paymentTypes.ts';
import paymentRoutes from './routes/payments.ts';
import expenditureRoutes from './routes/expenditures.ts';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/myparish')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/families', familyRoutes);
app.use('/api/offerings', offeringRoutes);
app.use('/api/offering-types', offeringTypeRoutes);
app.use('/api/payment-types', paymentTypeRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/events', eventRoutes);
app.use('/api/users', userRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/expenditures', expenditureRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
