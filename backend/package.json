{"name": "myparish-backend", "version": "1.0.0", "description": "Backend API for MyParish application", "main": "src/index.ts", "type": "module", "scripts": {"start": "node src/index.ts", "dev": "nodemon src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}